import { Book } from '@/pages/book/BookContext';
import { checkBookNotFree } from '@/pages/book/helper';
import moment from 'moment';
import { useState } from 'react';
import { toast } from 'react-toastify';
import { removeFavoriteBook } from '@/api/favorite-book/fetchers';
import DeactivateBookModal from '@/components/deactivate-book-modal';
import { findBooksByCode } from '@/api/bookCode/fetchers';
import { ActiveBookSource } from '@/api/bookCode/type';
import { IExamBookProductItem, IBookActivationInfo } from '@/types/book/index';
import ChangeActivatedBook from '@/pages/on-luyen/ChangeActivatedBook';
import { useLocation, useSearchParams } from 'react-router';

interface Props {
  book: Book;
  onDeactivate?: () => void;
}
const ActivateInfo = ({ book, onDeactivate }: Props) => {
  const [showDeactivateModal, setShowDeactivateModal] = useState(false);
  const [isDeactivating, setIsDeactivating] = useState(false);
  const [showReactivateModal, setShowReactivateModal] = useState(false);
  const [isReactivating, setIsReactivating] = useState(false);
  const [availableBooks, setAvailableBooks] = useState<IExamBookProductItem[]>(
    [],
  );
  const [currentBookStudyProgram, setCurrentBookStudyProgram] =
    useState<string>('');

  const [searchParams] = useSearchParams();
  const [bookActivationInfo, setBookActivationInfo] = useState<
    IBookActivationInfo | undefined
  >();
  const isPaidBook = checkBookNotFree(book.kindOfBook);
  const createdAt = moment(book?.bookUser?.createdDate || '');
  const expiryDate = moment(book?.bookUser?.expiryDate || '');
  const isExpired = expiryDate.isBefore(createdAt.clone().add(5, 'days'));
  const location = useLocation();
  const handleDeactivate = async () => {
    try {
      setIsDeactivating(true);
      await removeFavoriteBook(book.id);

      // Refresh OnLuyen data
      // if this causes state revert
      // if ((window as any).invalidateOnLuyenData) {
      //   (window as any).invalidateOnLuyenData();
      // }

      // Update parent component first
      if (onDeactivate) {
        onDeactivate();
      }

      toast.error('Đã hủy kích hoạt chương trình.');
      setShowDeactivateModal(false);
      //eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      const errorMessage =
        error.response?.data?.message || 'Có lỗi xảy ra khi hủy kích hoạt';

      if (error.response?.data?.message === 'Not favorite yet') {
        toast.error('Sách chưa được kích hoạt miễn phí');
      } else if (error.response?.data?.message === 'Book not found') {
        toast.error('Không tìm thấy sách');
      } else {
        toast.error(errorMessage);
      }
    } finally {
      setIsDeactivating(false);
    }
  };

  const activationCode = searchParams.get('activationCode');
  const exchangeable = location.state?.exchangeable;
  const handleOnclickChange = async () => {
    // Get the activation code from bookUser

    if (activationCode) {
      try {
        setIsReactivating(true);
        const response = await findBooksByCode(
          activationCode,
          {
            activeBookSource: ActiveBookSource.OnLuyen,
            skipCount: 0,
            maxResultCount: 100,
            isActivated: true,
          },
          false,
          true,
        );

        const books: IExamBookProductItem[] =
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          response.data?.items?.map((item: any) => ({
            bookId: item.id,
            name: item.name,
            examBookProductItemId: item.id, // Backend BookDto có field id
            title: item.title || item.name,
            description: item.description,
            gradeId: item.gradeId,
            subjectId: item.subjectId,
            studyProgrammeId: item.studyProgramBook?.id,
            studyProgrammeName: item.studyProgramBook?.name,
            kindOfBook: item.kindOfBook,
            type: item.type,
            numericalOrder: item.numericalOrder,
            isFavorite: item.isFavorite,
            isBought: item.isBought,
            part: item.part,
            code: item.code,
            status: item.status,
            price: item.price,
            bookUser: item.bookUser,
            studyProgramBook: item.studyProgramBook,
          })) || [];

        setAvailableBooks(books);

        const currentBookFromApi = response.data?.items?.find(
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          (item: any) => item.id === book.id,
        );
        if (currentBookFromApi?.studyProgramBook?.name) {
          setCurrentBookStudyProgram(currentBookFromApi.studyProgramBook);
        }
        // Set book activation info
        setBookActivationInfo({
          code: activationCode,
          activationDate:
            book.bookUser?.createdDate || new Date().toISOString(),
        });

        setShowReactivateModal(true);
      } catch (error) {
        console.error('Failed to fetch available books:', error);
        toast.error(
          'Không thể tải danh sách sách có thể đổi. Vui lòng thử lại sau.',
        );
      } finally {
        setIsReactivating(false);
      }
    } else {
      toast.error('Không tìm thấy mã kích hoạt. Vui lòng liên hệ hỗ trợ.');
    }
  };
  return (
    <div className="w-sidebar h-fit  border border-border p-4  flex flex-col gap-4  rounded-[12px] ">
      <h2 className="text-title text-xl font-semibold leading-[30px]">
        Thông tin kích hoạt
      </h2>

      {
        //không phải sách kích hoạt, không phải sách trả tiền, là sách free
        !book.bookUser && book.isFavorite && !isPaidBook ? ( // && isPaidBook
          <div className="text-gray-600 text-base leading-[24px] font-medium ">
            Chương trình đang được kích hoạt{' '}
            <span className="text-[#15A662] font-bold">miễn phí.</span>
            <br />
            Có thể cần mã kích hoạt nếu chính sách thay đổi.
          </div>
        ) : (
          <div className="flex flex-col justify-between gap-4 text-base leading-[24px] 	font-medium text-gray-600">
            <div>
              Chương trình đã được kích hoạt{' '}
              <span className="text-warning font-bold">bằng mã.</span>
            </div>
            <div>
              <div>Ngày kích hoạt: {createdAt.format('DD/MM/YYYY')}</div>
              <div>Ngày hết hạn: {expiryDate.format('DD/MM/YYYY')}</div>
            </div>
          </div>
        )
      }
      {
        //không được kích hoạt và là sách free
        !book.bookUser && book.isFavorite && !isPaidBook ? (
          <button
            onClick={() => setShowDeactivateModal(true)}
            disabled={isDeactivating}
            className="border cursor-pointer text-base border-border leading-[24px] rounded-[8px] font-bold px-[32px] py-[7px] text-gray-700 hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isDeactivating ? 'Đang xử lý...' : 'Hủy kích hoạt'}
          </button>
        ) : // đang false all do mã để test kích hoạt trước và đổi rồi
        //chưa hết hạn 5 day và exchangeable = true
        !isExpired && exchangeable == true ? (
          <button
            onClick={handleOnclickChange}
            disabled={isReactivating}
            className="border cursor-pointer text-base border-border leading-[24px] rounded-[8px] font-bold px-[32px] py-[7px] text-gray-700 hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isReactivating ? 'Đang tải...' : 'Đổi kích hoạt'}
          </button>
        ) : null
      }

      {/* Modal xác nhận hủy kích hoạt */}
      <DeactivateBookModal
        show={showDeactivateModal}
        bookName={book.name}
        onConfirm={handleDeactivate}
        onCancel={() => setShowDeactivateModal(false)}
        isLoading={isDeactivating}
      />

      {/* Modal đổi kích hoạt */}
      <ChangeActivatedBook
        show={showReactivateModal}
        handleClose={() => setShowReactivateModal(false)}
        book={{
          kindOfBook: book.kindOfBook,
          bookId: book.id,
          name: book.name,
          examBookProductItemId: book.id,
          title: book.name,
          description: book.description,
          studyProgramBook: currentBookStudyProgram,
        }}
        bookActivationInfo={bookActivationInfo}
        listBookChange={availableBooks}
        examBookProductId={book.id}
        setting={{ examPreparationProduct: 'onluyen' }}
      />
    </div>
  );
};

export default ActivateInfo;
