import { useEffect, useState } from 'react';
import NoStudyPlan from './components/NoStudyPlan';
import UpsertStudyPlanModal from './components/UpsertStudyPlanModal';
import { StudyPlan as StudyPlanType } from './helper';
import Splash from '@/components/splash/Splash';
import { useGetCurrentStudyPlan } from '@/api/study-plan/queries';
import PlanSumary from './components/PlanSumary';
import DeleteModal from './components/DeleteModal';
import PlanHeader from './components/PlanHeader';
import PlanCalendar from './components/PlanCalendar';
import PlanProgess from './components/PlanProgess';
import PlanTimeChart from './components/PlanTimeChart';
import StopStudyPlan from './components/StopStudyPlan';
import { useMinMediumScreen } from '@/hooks/UseMediaScreen';
const StudyPlan = () => {
  const isMediumScreen = useMinMediumScreen();
  const [studyPlan, setStudyPlan] = useState<StudyPlanType>(
    {} as StudyPlanType,
  );
  const [isLoading, setIsLoading] = useState(false);
  const [openUpsertModal, setOpenUpsertModal] = useState(false);
  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const [openStopModal, setOpenStopModal] = useState(false);
  const { data: studyPlansData, isLoading: isLoadingGetList } =
    useGetCurrentStudyPlan();

  useEffect(() => {
    if (studyPlansData) {
      setStudyPlan(studyPlansData.data.data);
    }
  }, [studyPlansData]);

  useEffect(() => {
    setIsLoading(isLoadingGetList);
  }, [isLoadingGetList]);

  if (isLoading) {
    return <Splash animated={false} logo={false} />;
  }

  return (
    <>
      {studyPlan?.id ? (
        <div className="flex flex-col gap-4 p-6 h-full container flex-1 overflow-y-auto hide-scrollbar">
          <PlanHeader
            setOpenDeleteModal={setOpenDeleteModal}
            setOpenUpsertModal={setOpenUpsertModal}
            setOpenStopModal={setOpenStopModal}
          />
          <div
            className={`flex gap-4 ${isMediumScreen ? 'flex-row' : 'flex-col'}`}
          >
            <PlanSumary studyPlan={studyPlan} />
            <PlanProgess studyPlan={studyPlan} />
          </div>
          <PlanCalendar studyPlan={studyPlan} />
          <PlanTimeChart studyPlan={studyPlan} />
        </div>
      ) : (
        <NoStudyPlan setOpenCreateModal={setOpenUpsertModal} />
      )}
      <UpsertStudyPlanModal
        open={openUpsertModal}
        setOpen={setOpenUpsertModal}
        initialData={studyPlan}
        setStudyPlan={setStudyPlan}
      />
      <StopStudyPlan
        studyPlan={studyPlan}
        open={openStopModal}
        setOpen={setOpenStopModal}
        setStudyPlan={setStudyPlan}
      />
      <DeleteModal
        studyPlan={studyPlan}
        open={openDeleteModal}
        setOpen={setOpenDeleteModal}
        setStudyPlan={setStudyPlan}
        setOpenStopModal={setOpenStopModal}
      />
    </>
  );
};

export default StudyPlan;
