import { useEffect, useState } from 'react';
import NoStudyPlan from './components/NoStudyPlan';
import UpsertStudyPlanModal from './components/UpsertStudyPlanModal';
import { StudyPlan as StudyPlanType } from './helper';
import Splash from '@/components/splash/Splash';
import { useGetCurrentStudyPlan } from '@/api/study-plan/queries';
import PlanSumary from './components/PlanSumary';
import DeleteModal from './components/DeleteModal';
import PlanHeader from './components/PlanHeader';
import PlanCalendar from './components/PlanCalendar';
import PlanProgess from './components/PlanProgess';
import PlanTimeChart from './components/PlanTimeChart';
import StopStudyPlan from './components/StopStudyPlan';

const StudyPlan = () => {
  const [studyPlan, setStudyPlan] = useState<StudyPlanType>(
    {} as StudyPlanType,
  );
  const [isLoading, setIsLoading] = useState(false);
  const [openUpsertModal, setOpenUpsertModal] = useState(false);
  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const [openStopModal, setOpenStopModal] = useState(false);
  const [layoutKey, setLayoutKey] = useState(0);
  const { data: studyPlansData, isLoading: isLoadingGetList } =
    useGetCurrentStudyPlan();

  useEffect(() => {
    if (studyPlansData) {
      setStudyPlan(studyPlansData.data.data);
    }
  }, [studyPlansData]);

  useEffect(() => {
    setIsLoading(isLoadingGetList);
  }, [isLoadingGetList]);

  // Reset viewport and ensure proper responsive behavior
  useEffect(() => {
    // Force recalculation of responsive breakpoints
    const resetResponsive = () => {
      // Force CSS media queries to re-evaluate using matchMedia
      const mdQuery = window.matchMedia('(min-width: 768px)');
      const smQuery = window.matchMedia('(min-width: 640px)');

      // Force re-evaluation by accessing matches property
      mdQuery.matches; // Force evaluation
      smQuery.matches; // Force evaluation

      // Trigger resize events multiple times to ensure all components respond
      setTimeout(() => window.dispatchEvent(new Event('resize')), 0);
      setTimeout(() => window.dispatchEvent(new Event('resize')), 50);
      setTimeout(() => window.dispatchEvent(new Event('resize')), 100);

      // Force reflow by temporarily changing body style
      const originalMinWidth = document.body.style.minWidth;
      document.body.style.minWidth = '320px';
      document.body.offsetHeight; // Force reflow
      document.body.style.minWidth = originalMinWidth;
    };

    // Reset immediately and after a short delay
    resetResponsive();
    const timer = setTimeout(() => {
      resetResponsive();
      // Force re-render by changing key
      setLayoutKey((prev) => prev + 1);
    }, 150);

    return () => {
      clearTimeout(timer);
    };
  }, []);

  if (isLoading) {
    return <Splash animated={false} logo={false} />;
  }

  return (
    <>
      {studyPlan?.id ? (
        <div className="w-full min-h-screen" style={{ minWidth: '320px' }}>
          <div
            className="flex flex-col gap-4 p-4 sm:p-6 max-w-full container mx-auto flex-1 overflow-y-auto hide-scrollbar"
            style={
              {
                width: '100%',
                maxWidth: '100%',
                '--force-responsive-reset': '1',
              } as React.CSSProperties
            }
          >
            <PlanHeader
              setOpenDeleteModal={setOpenDeleteModal}
              setOpenUpsertModal={setOpenUpsertModal}
              setOpenStopModal={setOpenStopModal}
            />
            <div
              className="flex flex-col md:flex-row gap-4 w-full"
              key={`summary-progress-container-${layoutKey}`}
              style={{
                display: 'flex',
                flexDirection: window.innerWidth >= 768 ? 'row' : 'column',
                gap: '1rem',
                width: '100%',
              }}
            >
              <PlanSumary studyPlan={studyPlan} />
              <PlanProgess studyPlan={studyPlan} />
            </div>
            <PlanCalendar studyPlan={studyPlan} />
            <PlanTimeChart studyPlan={studyPlan} />
          </div>
        </div>
      ) : (
        <NoStudyPlan setOpenCreateModal={setOpenUpsertModal} />
      )}
      <UpsertStudyPlanModal
        open={openUpsertModal}
        setOpen={setOpenUpsertModal}
        initialData={studyPlan}
        setStudyPlan={setStudyPlan}
      />
      <StopStudyPlan
        studyPlan={studyPlan}
        open={openStopModal}
        setOpen={setOpenStopModal}
        setStudyPlan={setStudyPlan}
      />
      <DeleteModal
        studyPlan={studyPlan}
        open={openDeleteModal}
        setOpen={setOpenDeleteModal}
        setStudyPlan={setStudyPlan}
        setOpenStopModal={setOpenStopModal}
      />
    </>
  );
};

export default StudyPlan;
