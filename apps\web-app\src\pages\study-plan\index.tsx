import { useEffect, useState } from 'react';
import NoStudyPlan from './components/NoStudyPlan';
import UpsertStudyPlanModal from './components/UpsertStudyPlanModal';
import { StudyPlan as StudyPlanType } from './helper';
import Splash from '@/components/splash/Splash';
import { useGetCurrentStudyPlan } from '@/api/study-plan/queries';
import PlanSumary from './components/PlanSumary';
import DeleteModal from './components/DeleteModal';
import PlanHeader from './components/PlanHeader';
import PlanCalendar from './components/PlanCalendar';
import PlanProgess from './components/PlanProgess';
import PlanTimeChart from './components/PlanTimeChart';
import StopStudyPlan from './components/StopStudyPlan';

const StudyPlan = () => {
  const [studyPlan, setStudyPlan] = useState<StudyPlanType>(
    {} as StudyPlanType,
  );
  const [isLoading, setIsLoading] = useState(false);
  const [openUpsertModal, setOpenUpsertModal] = useState(false);
  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const [openStopModal, setOpenStopModal] = useState(false);
  const { data: studyPlansData, isLoading: isLoadingGetList } =
    useGetCurrentStudyPlan();

  useEffect(() => {
    if (studyPlansData) {
      setStudyPlan(studyPlansData.data.data);
    }
  }, [studyPlansData]);

  useEffect(() => {
    setIsLoading(isLoadingGetList);
  }, [isLoadingGetList]);

  // Reset viewport and ensure proper responsive behavior
  useEffect(() => {
    // Force a reflow to ensure responsive classes are applied correctly
    const forceReflow = () => {
      document.body.style.display = 'none';
      document.body.offsetHeight; // Trigger reflow
      document.body.style.display = '';
    };

    // Small delay to ensure DOM is ready
    const timer = setTimeout(forceReflow, 100);

    // Also trigger on window resize to handle viewport changes
    const handleResize = () => {
      // Force recalculation of responsive breakpoints
      window.dispatchEvent(new Event('resize'));
    };

    handleResize();

    return () => {
      clearTimeout(timer);
    };
  }, []);

  if (isLoading) {
    return <Splash animated={false} logo={false} />;
  }

  return (
    <>
      {studyPlan?.id ? (
        <div className="w-full min-h-screen" style={{ minWidth: '320px' }}>
          <div
            className="flex flex-col gap-4 p-4 sm:p-6 max-w-full container mx-auto flex-1 overflow-y-auto hide-scrollbar"
            style={{ width: '100%', maxWidth: '100%' }}
          >
            <PlanHeader
              setOpenDeleteModal={setOpenDeleteModal}
              setOpenUpsertModal={setOpenUpsertModal}
              setOpenStopModal={setOpenStopModal}
            />
            <div className="flex flex-col xl:flex-row gap-4 w-full">
              <PlanSumary studyPlan={studyPlan} />
              <PlanProgess studyPlan={studyPlan} />
            </div>
            <PlanCalendar studyPlan={studyPlan} />
            <PlanTimeChart studyPlan={studyPlan} />
          </div>
        </div>
      ) : (
        <NoStudyPlan setOpenCreateModal={setOpenUpsertModal} />
      )}
      <UpsertStudyPlanModal
        open={openUpsertModal}
        setOpen={setOpenUpsertModal}
        initialData={studyPlan}
        setStudyPlan={setStudyPlan}
      />
      <StopStudyPlan
        studyPlan={studyPlan}
        open={openStopModal}
        setOpen={setOpenStopModal}
        setStudyPlan={setStudyPlan}
      />
      <DeleteModal
        studyPlan={studyPlan}
        open={openDeleteModal}
        setOpen={setOpenDeleteModal}
        setStudyPlan={setStudyPlan}
        setOpenStopModal={setOpenStopModal}
      />
    </>
  );
};

export default StudyPlan;
