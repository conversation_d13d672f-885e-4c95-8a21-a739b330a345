import { Button } from '@workspace/ui/components/button';
import {
  <PERSON><PERSON>,
  <PERSON>alogBody,
  Dialog<PERSON>ontent,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from '@workspace/ui/components/dialog';
import { useState } from 'react';
import { StudyPlan } from '../helper';
import { stopStudyPlan } from '@/api/study-plan/fetchers';
import { useQueryClient } from 'react-query';
import { QueryKeys } from '@/api/study-plan/queries';
import { toast } from 'react-toastify';

type Props = {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  studyPlan: StudyPlan;
  setStudyPlan: React.Dispatch<React.SetStateAction<StudyPlan>>;
};

function StopStudyPlan({ open, setOpen, studyPlan, setStudyPlan }: Props) {
  const [isLoading, setIsLoading] = useState(false);
  const queryClient = useQueryClient();
  const handleConfirm = async () => {
    try {
      setIsLoading(true);
      const id = studyPlan?.id;
      if (!id) return;
      const res = await stopStudyPlan(id);
      if (res.status === 200) {
        setOpen(false);
        setStudyPlan({} as StudyPlan);
        queryClient.refetchQueries({
          queryKey: [QueryKeys.ENDED_STUDY_PLANS],
        });
        toast.success('Đã kết thúc mục tiêu!');
      }
    } finally {
      setIsLoading(false);
    }
  };
  const handleCancel = () => {
    setOpen(false);
    setIsLoading(false);
  };
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      handleCancel();
    }
  };
  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-150 max-h-[80vh] h-fit flex flex-col mx-4">
        <DialogHeader>
          <DialogTitle className="text-lg sm:text-xl font-semibold">
            Kết thúc mục tiêu
          </DialogTitle>
        </DialogHeader>
        <DialogBody className="font-medium text-sm sm:text-base text-body flex flex-col gap-4 flex-1 h-fit overflow-auto hide-scrollbar">
          <span>Bạn có chắc chắn muốn kết thúc mục tiêu này?</span>
          <div className="flex rounded-md px-3 sm:px-4 py-3 gap-2 bg-yellow-100 border border-yellow">
            <ul className="list-disc pl-4">
              <li className="text-sm sm:text-base text-body font-medium leading-[1.5]">
                Ngày hôm nay sẽ được ghi là ngày kết thúc mục tiêu.
              </li>
              <li className="text-sm sm:text-base text-body font-medium leading-[1.5]">
                Hệ thống sẽ không ghi nhận thêm kết quả cho mục tiêu này.
              </li>
              <li className="text-sm sm:text-base text-body font-medium leading-[1.5]">
                Bạn vẫn có thể xem lại lịch sử mục tiêu.
              </li>
            </ul>
          </div>
          <span>
            Bạn có thể tạo mục tiêu mới sau khi đã kết thúc mục tiêu hiện tại.
          </span>
        </DialogBody>
        <DialogFooter className="flex-col sm:flex-row gap-2">
          <Button
            variant="light"
            onClick={handleCancel}
            type="button"
            className="w-full sm:w-auto"
          >
            Hủy
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={isLoading}
            className="w-full sm:w-auto"
          >
            Kết thúc
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default StopStudyPlan;
