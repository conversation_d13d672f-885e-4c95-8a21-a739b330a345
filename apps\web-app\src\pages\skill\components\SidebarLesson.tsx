import React, { useEffect, useMemo, useRef, useCallback } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router';
import SVG from 'react-inlinesvg';
import { Lesson } from '@/pages/book/BookContext';
import SkillItem from './SkillItem';
import {
  Tooltip,
  TooltipArrow,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@workspace/ui/components/tooltip';
import { useSkillContext } from '../SkillContext';
import { SkillType } from '@/helpers/types';

type Props = {
  lesson: Lesson;
  selectedLessonId: string;
  setSelectedLessonId: React.Dispatch<React.SetStateAction<string>>;
};
const SidebarLesson = ({
  lesson,
  selectedLessonId,
  setSelectedLessonId,
}: Props) => {
  const navigate = useNavigate();
  const { setIndexQuestion, setCurrentSkill } = useSkillContext();
  const { id } = useParams();
  const isFirstTimeAccess = useRef(true);
  const [searchParams] = useSearchParams();
  const lessonId = useMemo(() => searchParams.get('lessonId'), [searchParams]);

  const allSkills = useMemo(() => {
    return lesson.sections?.flatMap((section) => section.skills || []) || [];
  }, [lesson]);

  useEffect(() => {
    if (lessonId && isFirstTimeAccess.current == true) {
      setSelectedLessonId(lessonId);
      isFirstTimeAccess.current = false;
    }
  }, [lessonId, setSelectedLessonId]);

  const hasSkill = useMemo(
    () => (lesson: Lesson) => {
      const lessonSkill = lesson?.skills || [];
      const sectionsSkill = lesson?.sections?.flatMap((s) => s?.skills) || [];
      return lessonSkill?.length > 0 || sectionsSkill?.length > 0;
    },
    [lesson],
  );

  const isSelectedLesson = useMemo(
    () => selectedLessonId === lesson.id,
    [selectedLessonId, lesson.id],
  );

  const handleClick = useCallback(
    (skillId: string) => {
      const target = searchParams.get('target');
      const index = allSkills.findIndex((s) => s.id === skillId);

      if (target === 'lms') {
        window.open(`/skill/${skillId}?lessonId=${lesson.id}`, '_blank');
      } else {
        const skill = allSkills.find((s) => s.id === skillId);
        if (skill?.type === SkillType.Checkpoint) {
          navigate(`/checkpoints/${skillId}?lessonId=${lesson.id}`);
        } else {
          setCurrentSkill(index);
          setIndexQuestion(0);
          setTimeout(() => {
            navigate(`/skill/${skillId}?lessonId=${lesson.id}`);
          }, 0);
        }
      }
    },
    [
      navigate,
      searchParams,
      lesson.id,
      allSkills,
      setCurrentSkill,
      setIndexQuestion,
    ],
  );

  if (!lesson.name) return <></>;

  let skillIndex = 0;

  return (
    <div>
      <div
        key={lesson.id}
        onClick={() =>
          hasSkill(lesson) &&
          setSelectedLessonId(lesson.id === selectedLessonId ? '' : lesson.id)
        }
        className={`flex gap-2 items-center px-2 py-3 pl-10 cursor-pointer ${hasSkill(lesson) && 'cursor-pointer hover:bg-blue-100'} ${
          isSelectedLesson ? 'bg-blue-100' : ''
        }`}
      >
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger className="flex-1 flex w-full">
              <span
                className={`flex-1 text-gray-700 text-[14px] text-truncate font-medium leading-[1.5] text-left cursor-pointer`}
                style={{ width: '180px' }}
              >
                {lesson.name}
              </span>
            </TooltipTrigger>

            <TooltipContent className="px-2 py-1 text-xs text-white bg-gray-600 rounded-lg shadow-md">
              {lesson.name}
              <TooltipArrow />
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>

        {hasSkill(lesson) && (
          <span
            className={`transition-transform text-gray-800 ${isSelectedLesson && 'rotate-90'}`}
          >
            <SVG
              src={`/svg/sidebar/arrow-right.svg`}
              width={14}
              height={14}
              className="text-body"
            />
          </span>
        )}
      </div>
      {isSelectedLesson && (
        <div>
          {lesson?.sections?.map((section) => (
            <React.Fragment key={section.id}>
              {section?.name && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger className="flex-1 flex w-full">
                      <span
                        className="gap-2 pl-[16px] pr-2 py-3 flex-1 text-truncate text-[13px] text-gray-500 font-medium text-left"
                        style={{ width: '180px' }}
                      >
                        {section?.name}
                      </span>
                    </TooltipTrigger>

                    <TooltipContent className="px-2 py-1 text-xs text-white bg-gray-600 rounded-lg shadow-md">
                      {section.name}
                      <TooltipArrow />
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
              {section.skills?.map((skill) => {
                skillIndex++;
                return (
                  <SkillItem
                    key={skill.id}
                    skill={skill}
                    index={skillIndex}
                    isSelected={skill.id === id}
                    onClick={(skillId: string) => handleClick(skillId)}
                  />
                );
              })}
            </React.Fragment>
          ))}
        </div>
      )}
    </div>
  );
};

export default SidebarLesson;
