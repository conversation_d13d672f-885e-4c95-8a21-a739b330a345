import { useEffect, useMemo, useState } from 'react';
import { Button } from '@workspace/ui/components/button';
import {
  endOfMonth,
  addDays,
  subDays,
  getDay,
  getDate,
  getMonth,
  getYear,
  addMonths,
  subMonths,
  format,
  isSameDay,
  parseISO,
} from 'date-fns';
import { ChevronLeft, ChevronRight, Clock } from 'lucide-react';
import SVG from 'react-inlinesvg';
import { cn } from '@/lib/utils';
import { useGetDaysStatus } from '@/api/study-plan/queries';
import {
  DailyStudyStatus,
  daysOfWeek,
  renderTimeStudy,
  StudyDayStatus,
  StudyPlan,
  Weekdays,
  weekdaysMap2,
} from '../helper';
import { useNavigate } from 'react-router';

type Props = {
  studyPlan: StudyPlan;
};

enum TimeType {
  PAST = 'past',
  TODAY = 'today',
  FUTURE = 'future',
}

const getMonthRange = (year: number, month: number) => {
  const firstDate = new Date(year, month - 1, 1);
  const lastDate = endOfMonth(firstDate);
  const start = subDays(firstDate, (getDay(firstDate) + 6) % 7);
  const end = addDays(lastDate, (7 - getDay(lastDate)) % 7);
  return { start, end };
};

const PlanCalendar = ({ studyPlan }: Props) => {
  const today = new Date();
  const [viewDate, setViewDate] = useState(
    () => new Date(today.getFullYear(), today.getMonth(), 1),
  );
  const viewYear = getYear(viewDate);
  const viewMonth = getMonth(viewDate) + 1;
  const { start: matrixStart, end: matrixEnd } = useMemo(
    () => getMonthRange(viewYear, viewMonth),
    [viewYear, viewMonth],
  );

  const start = new Date(studyPlan.startDate);
  const end = studyPlan.endDate
    ? new Date(studyPlan.endDate)
    : addMonths(today, 2);
  const canGoPrev = viewDate > start;
  const canGoNext =
    addMonths(viewDate, 1) <= addMonths(today, 2) &&
    addMonths(viewDate, 1) <= end;
  const { data: daysData } = useGetDaysStatus(
    studyPlan?.id || '',
    format(matrixStart, 'yyyy-MM-dd'),
    format(matrixEnd, 'yyyy-MM-dd'),
  );
  const [daysStatus, setDaysStatus] = useState<StudyDayStatus[]>([]);
  const [selectedDate, setSelectedDate] = useState(() => new Date());
  const navigate = useNavigate();
  const matrix = useMemo(() => {
    const startDate = new Date(parseISO(studyPlan.startDate));
    const endDate = studyPlan.endDate
      ? new Date(parseISO(studyPlan.endDate))
      : null;
    const firstDate = new Date(viewYear, viewMonth - 1, 1);
    const lastDate = endOfMonth(firstDate);
    const start = subDays(firstDate, (getDay(firstDate) + 6) % 7);
    const end = addDays(lastDate, (7 - getDay(lastDate)) % 7);
    const today = new Date();
    const weeks = [];
    let current = start;
    while (current <= end) {
      const week = [];

      for (let i = 0; i < 7; i++) {
        const date = current;
        const dateStr = format(date, 'yyyy-MM-dd');
        const match = daysStatus.find(
          (d) => format(d.date, 'yyyy-MM-dd') === dateStr,
        );

        let status = DailyStudyStatus.NoPlan;
        let duration = 0;

        const weekday = (getDay(date) + 6) % 7;
        if (match) {
          status = match.status;
          duration = match.targetDuration;
        } else {
          if ((studyPlan.weekdays & (1 << weekday)) !== 0) {
            duration = studyPlan.duration;
            if (
              date > today &&
              date >= startDate &&
              (endDate === null || date <= endDate)
            ) {
              status = DailyStudyStatus.Future;
            }
          }
        }

        let timeType: TimeType = TimeType.PAST;
        if (isSameDay(date, today)) {
          timeType = TimeType.TODAY;
          if (
            status === DailyStudyStatus.NoPlan &&
            (studyPlan.weekdays & (1 << weekday)) !== 0 &&
            date >= startDate &&
            (endDate === null || date <= endDate)
          ) {
            status = DailyStudyStatus.Missing;
          }
        } else if (date > today) {
          timeType = TimeType.FUTURE;
        }

        week.push({
          day: getDate(date),
          status,
          duration,
          date,
          timeStudy: match?.timeStudy ? match.timeStudy / (60 * 1000) : 0,
          timeType,
        });

        current = addDays(current, 1);
      }

      weeks.push(week);
    }

    return weeks;
  }, [viewDate, daysStatus, studyPlan]);

  useEffect(() => {
    if (daysData) {
      setDaysStatus(daysData.data.data);
    }
  }, [daysData]);

  const totalPlanDays = useMemo(() => {
    const viewMonthStr = format(viewDate, 'yyyy-MM');
    return matrix
      .flat()
      .filter(
        (d) =>
          d.status !== DailyStudyStatus.NoPlan &&
          format(d.date, 'yyyy-MM') === viewMonthStr,
      ).length;
  }, [matrix, viewDate]);

  const totalDoneDays = useMemo(() => {
    const viewMonthStr = format(viewDate, 'yyyy-MM');
    return matrix
      .flat()
      .filter(
        (d) =>
          d.status === DailyStudyStatus.Done &&
          format(d.date, 'yyyy-MM') === viewMonthStr,
      ).length;
  }, [matrix, viewDate]);

  const totalMissingDays = useMemo(() => {
    const viewMonthStr = format(viewDate, 'yyyy-MM');
    return matrix
      .flat()
      .filter(
        (d) =>
          d.status === DailyStudyStatus.Missing &&
          format(d.date, 'yyyy-MM') === viewMonthStr &&
          d.timeType === TimeType.PAST,
      ).length;
  }, [matrix, viewDate]);

  const totalNotEnoughDays = useMemo(() => {
    const viewMonthStr = format(viewDate, 'yyyy-MM');
    return matrix
      .flat()
      .filter(
        (d) =>
          d.status === DailyStudyStatus.NotEnough &&
          format(d.date, 'yyyy-MM') === viewMonthStr &&
          d.timeType === TimeType.PAST,
      ).length;
  }, [matrix, viewDate]);

  const totalFutureDays = useMemo(() => {
    const viewMonthStr = format(viewDate, 'yyyy-MM');
    return matrix
      .flat()
      .filter(
        (d) =>
          d.status === DailyStudyStatus.Future &&
          format(d.date, 'yyyy-MM') === viewMonthStr,
      ).length;
  }, [matrix, viewDate]);

  const getNextStudyDateFromBitmask = (
    fromDate: Date,
    bitmask: Weekdays,
    endDate?: Date | null,
  ): Date | null => {
    if (fromDate < parseISO(studyPlan.startDate))
      fromDate = parseISO(studyPlan.startDate);
    const weekdayIndices = [
      Weekdays.Sunday,
      Weekdays.Monday,
      Weekdays.Tuesday,
      Weekdays.Wednesday,
      Weekdays.Thursday,
      Weekdays.Friday,
      Weekdays.Saturday,
    ]
      .map((flag, index) => ((bitmask & flag) !== 0 ? index : -1))
      .filter((i) => i !== -1);
    for (let i = 0; i <= 6; i++) {
      const next = addDays(fromDate, i);
      if (endDate && next > endDate) break;
      if (weekdayIndices.includes(getDay(next))) {
        return next;
      }
    }

    return null;
  };
  const renderIcon = (status: DailyStudyStatus, timeType: TimeType) => {
    switch (true) {
      case status === DailyStudyStatus.Missing && timeType !== TimeType.TODAY:
        return <img src="/svg/study-plan/x.png" />;
      case status === DailyStudyStatus.NotEnough && timeType !== TimeType.TODAY:
        return <SVG src="/svg/study-plan/warning.svg" />;
      case status === DailyStudyStatus.Done:
        return <SVG src="/svg/study-plan/check.svg" />;
    }
  };

  const statusMap = (
    status: DailyStudyStatus,
    timeType: TimeType,
    timeStudy: number,
    duration: number,
  ) => {
    switch (true) {
      case status === DailyStudyStatus.Missing:
        return (
          <span className="text-danger text-base font-medium">
            {timeType === TimeType.TODAY ? 'Chưa học' : 'Bỏ lỡ'}
          </span>
        );
      case status === DailyStudyStatus.NotEnough:
        return timeType === TimeType.TODAY ? (
          <span className="text-primary text-base font-medium">Đang học</span>
        ) : (
          <span className="text-warning text-base font-medium flex flex-col gap-1">
            Học chưa đủ
            <span className="text-placeholder text-sm font-medium">
              <span className="text-base text-body">
                {renderTimeStudy(timeStudy, duration)}
              </span>
              /{duration} phút
            </span>
          </span>
        );
      case status === DailyStudyStatus.Done:
        return (
          <span className="text-success text-base font-medium">Hoàn thành</span>
        );
      case status === DailyStudyStatus.Future:
        return (
          <span className="text-subtex text-base font-medium">
            Chưa đến lịch
          </span>
        );
    }
  };
  const renderDayInfo = () => {
    const matched = matrix.flat().find((d) => isSameDay(d.date, selectedDate));
    if (
      !matched ||
      (matched.status === DailyStudyStatus.NoPlan &&
        matched.timeType !== TimeType.TODAY)
    ) {
      return (
        <span className="font-medium text-[16px] leading-[150%] tracking-[0%] align-middle text-[#3F4254] font-inter">
          Không có lịch học.
        </span>
      );
    }
    const endDate = studyPlan.endDate ? parseISO(studyPlan.endDate) : null;
    const nextStudyDate = getNextStudyDateFromBitmask(
      selectedDate,
      studyPlan.weekdays,
      endDate,
    );

    if (
      matched.timeType === TimeType.TODAY &&
      matched.status === DailyStudyStatus.NoPlan
    ) {
      return (
        <div className="flex flex-col gap-2">
          <span className="font-medium text-[16px] leading-[150%] tracking-[0%] align-middle text-[#3F4254] font-inter">
            Bạn không có lịch học hôm nay.
          </span>
          {nextStudyDate && (
            <div className="font-medium text-[16px] leading-[150%] tracking-[0%] align-middle text-[#3F4254] font-inter">
              Ngày học sắp tới:
              <div className="font-semibold text-[#4B5ED4]">
                {
                  weekdaysMap2[
                    (1 <<
                      (getDay(nextStudyDate) + 6) %
                        7) as keyof typeof weekdaysMap2
                  ]
                }
                {', '}
                {format(nextStudyDate, 'dd/MM/yyyy')}
              </div>
            </div>
          )}
        </div>
      );
    }

    return (
      <div className="flex flex-col gap-3 xl:gap-3 mt-3 w-full">
        <div className="flex flex-col sm:flex-row xl:flex-col gap-3 xl:gap-3">
          <div className="flex flex-col sm:flex-row xl:flex-row gap-3 xl:gap-8 items-start sm:items-center">
            <div className="flex gap-2 items-center min-w-0 sm:min-w-32">
              <Clock className="w-5 h-5 text-gray-700 flex-none" />
              <span className="text-subtext font-semibold text-base">
                Thời lượng
              </span>
            </div>
            <p className="text-body text-base font-medium">
              {matched.duration} phút
            </p>
          </div>
          <div className="flex flex-col sm:flex-row xl:flex-row gap-3 xl:gap-8 items-start sm:items-center">
            <div className="flex gap-2 min-w-0 sm:min-w-32 items-center h-fit">
              <SVG
                src="/svg/study-plan/target.svg"
                className="w-5 h-5 text-gray-700 flex-none"
              />
              <span className="text-subtext font-semibold text-base">
                Trạng thái
              </span>
            </div>
            <div className="min-w-0">
              {statusMap(
                matched.status,
                matched.timeType,
                matched.timeStudy,
                matched.duration,
              )}
            </div>
          </div>
        </div>
        {matched.timeType === TimeType.TODAY && matched.status < 2 && (
          <div className="col-span-2 flex justify-center">
            <Button onClick={handleClickStudy}>
              {matched.status === DailyStudyStatus.NotEnough
                ? 'Tiếp tục học'
                : 'Vào học ngay'}
              <ChevronRight className="w-5 h-5 text-white" />
            </Button>
          </div>
        )}
      </div>
    );
  };

  const handleClickStudy = () => {
    navigate(`/thu-vien/on-luyen`);
  };

  return (
    <div className="p-4 pb-3 flex flex-col gap-3 w-full border border-border rounded-md shadow-small select-none overflow-hidden">
      <h3 className="text-base leading-[1.3] text-title font-semibold">
        Theo dõi lịch học
      </h3>
      <div className="flex flex-col xl:flex-row gap-6 min-w-0">
        <div className="flex flex-col gap-3">
          <div className="relative flex justify-center items-center gap-2 sm:gap-4 xl:gap-8">
            <Button
              variant="icon-ghost"
              size="sm"
              className="h-8 w-8 flex-shrink-0"
              onClick={() =>
                canGoPrev && setViewDate((prev) => subMonths(prev, 1))
              }
              disabled={!canGoPrev}
            >
              <ChevronLeft className="w-5.5 h-5.5 text-gray-700" />
            </Button>
            <h2 className="text-sm sm:text-base xl:text-lg font-semibold text-blue-700 text-center">
              Tháng {viewMonth}, {viewYear}
            </h2>
            <Button
              variant="icon-ghost"
              size="sm"
              className="h-8 w-8 flex-shrink-0"
              onClick={() =>
                canGoNext && setViewDate((prev) => addMonths(prev, 1))
              }
              disabled={!canGoNext}
            >
              <ChevronRight className="w-5.5 h-5.5 text-gray-700" />
            </Button>
            <Button
              variant="outline-light"
              size="sm"
              className="rounded-2xl border-gray-400 text-gray-700 shadow-small absolute left-0 top-0 text-xs sm:text-sm"
              onClick={() => {
                const today = new Date();
                setViewDate(new Date(today.getFullYear(), today.getMonth(), 1));
                setSelectedDate(today);
              }}
            >
              Hôm nay
            </Button>
          </div>

          <div className="border border-gray-300 rounded-lg overflow-hidden min-w-0">
            <table className="table-fixed w-full border-collapse min-w-[280px]">
              <thead className="text-white">
                <tr>
                  {daysOfWeek.map((d) => (
                    <th
                      key={d}
                      className={cn(
                        'bg-primary p-3 border-r border-gray-300 last:border-r-0',
                      )}
                    >
                      {d}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {matrix.map((week, weekIdx) => (
                  <tr
                    key={weekIdx}
                    className="border-t border-gray-300 first:border-t-0"
                  >
                    {week.map((cell, dayIdx) => {
                      return (
                        <td
                          key={dayIdx}
                          onClick={() => setSelectedDate(cell.date)}
                          className={cn(
                            'h-[90px] border-r border-gray-300 last:border-r-0 align-top p-2 bg-white font-medium cursor-pointer',
                            {
                              'p-3 text-title opacity-40':
                                cell.status === DailyStudyStatus.NoPlan,
                              'ring-2 ring-primary ring-inset opacity-100':
                                cell.status === DailyStudyStatus.NoPlan &&
                                isSameDay(selectedDate, cell.date),
                            },
                          )}
                        >
                          <div
                            className={cn({
                              'flex flex-col w-full h-full lg:justify-between py-2 px-1.5 rounded-md':
                                cell.status !== DailyStudyStatus.NoPlan,
                              'bg-gradient-to-b from-[#FFEFEC] to-[#FFD9D5] hover:from-[#FFD9D5] hover:to-[#F8B6B0]':
                                cell.status === DailyStudyStatus.Missing,
                              'bg-gradient-to-b from-[#FCFFDB] to-[#F7EAC6] hover:from-[#F7EAC6] hover:to-[#F1D996]':
                                cell.status === DailyStudyStatus.NotEnough,
                              'bg-gradient-to-b from-[#E4FEF4] to-[#C5F6E5] hover:from-[#C5F6E5] hover:to-[#9FE8CE]':
                                cell.status === DailyStudyStatus.Done,
                              'bg-gradient-to-b from-[#ECF6FF] to-[#D5ECFF] hover:from-[#D5ECFF] hover:to-[#A6D6FF]':
                                cell.status < 2 &&
                                cell.timeType === TimeType.TODAY,
                              'bg-gradient-to-b from-[#F7F7F7] to-[#E7E7E7] hover:from-[#E7E7E7] hover:to-[#D7D7D7]':
                                cell.status === DailyStudyStatus.Future,
                              'ring-2 ring-success ring-inset':
                                cell.status === DailyStudyStatus.Done &&
                                isSameDay(selectedDate, cell.date),
                              'ring-2 ring-danger ring-inset':
                                cell.status === DailyStudyStatus.Missing &&
                                isSameDay(selectedDate, cell.date),
                              'ring-2 ring-warning ring-inset':
                                cell.status === DailyStudyStatus.NotEnough &&
                                isSameDay(selectedDate, cell.date),
                              'ring-2 ring-primary ring-inset':
                                (cell.status < 2 &&
                                  cell.timeType === TimeType.TODAY &&
                                  isSameDay(selectedDate, cell.date)) ||
                                (cell.status === DailyStudyStatus.Future &&
                                  isSameDay(selectedDate, cell.date)),
                            })}
                          >
                            <span
                              className={cn(
                                'text-base font-medium text-title',
                                {
                                  'flex flex-col lg:flex-row gap-2':
                                    cell.status < 2 &&
                                    cell.timeType === TimeType.TODAY,
                                },
                              )}
                            >
                              {cell.day}
                              {cell.status < 2 &&
                                cell.timeType === TimeType.TODAY && (
                                  <span className="text-sm self-center font-medium text-blue-light hidden lg:inline overflow-hidden text-ellipsis whitespace-nowrap">
                                    {cell.duration}
                                    <span> phút</span>
                                  </span>
                                )}
                            </span>
                            {cell.status < 2 &&
                            cell.timeType === TimeType.TODAY ? (
                              <div className="flex flex-col items-center justify-center my-auto lg:self-auto">
                                <Button
                                  size="xs"
                                  className="text-xs font-semibold items-center hidden lg:flex gap-1"
                                  onClick={handleClickStudy}
                                >
                                  <span className="whitespace-nowrap overflow-hidden text-ellipsis">
                                    {cell.status === DailyStudyStatus.NotEnough
                                      ? 'Tiếp tục'
                                      : 'Vào học'}
                                  </span>
                                  <ChevronRight className="w-4 h-4 text-white" />
                                </Button>

                                <SVG
                                  onClick={handleClickStudy}
                                  src="/svg/study-plan/play.svg"
                                  className="block lg:hidden w-5 h-5 self-center"
                                />
                              </div>
                            ) : (
                              <span
                                className={cn(
                                  'text-sm font-medium flex justify-between my-auto lg:self-auto self-center',
                                  {
                                    'text-success':
                                      cell.status === DailyStudyStatus.Done,
                                    'text-yellow-600':
                                      cell.status ===
                                      DailyStudyStatus.NotEnough,
                                    'text-danger':
                                      cell.status === DailyStudyStatus.Missing,
                                    'text-subtext':
                                      cell.status === DailyStudyStatus.Future,
                                  },
                                )}
                              >
                                {cell.status !== DailyStudyStatus.NoPlan && (
                                  <span>
                                    {cell.duration}
                                    <span className="hidden lg:inline">
                                      {' '}
                                      phút
                                    </span>
                                  </span>
                                )}
                                {cell.status < 3 && (
                                  <div
                                    className={cn(
                                      'w-6 h-6 items-center justify-center hidden lg:flex rounded-[4px]',
                                      {
                                        'bg-[#e6f9eb]':
                                          cell.status === DailyStudyStatus.Done,
                                        'bg-[#fff5e9]':
                                          cell.status ===
                                          DailyStudyStatus.NotEnough,
                                        'bg-[#ffecec]':
                                          cell.status ===
                                          DailyStudyStatus.Missing,
                                      },
                                    )}
                                  >
                                    {renderIcon(cell.status, cell.timeType)}
                                  </div>
                                )}
                              </span>
                            )}
                          </div>
                        </td>
                      );
                    })}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
        <div className="flex flex-col gap-6 mt-6 xl:mt-11 flex-none xl:w-69 w-full min-w-0">
          <div className="grid grid-cols-1 sm:grid-cols-[auto_1fr] xl:grid-rows-4 gap-x-4 xl:gap-x-8 gap-y-3">
            {totalDoneDays > 0 && (
              <>
                <div className="flex gap-2 min-w-[130px] items-center">
                  <div className="w-6 h-6 flex items-center justify-center rounded-[4px] bg-[#e6f9eb]">
                    <SVG src="/svg/study-plan/check.svg" />
                  </div>
                  <span className="text-base font-semibold text-subtext">
                    Hoàn thành
                  </span>
                </div>
                <span className="text-base font-medium text-body">
                  {totalDoneDays}/{totalPlanDays}
                </span>
              </>
            )}
            {totalNotEnoughDays > 0 && (
              <>
                <div className="flex gap-2 min-w-[130px] items-center">
                  <div className="w-6 h-6 flex items-center justify-center rounded-[4px] bg-[#fff5e9]">
                    <SVG src="/svg/study-plan/warning.svg" />
                  </div>
                  <span className="text-base font-semibold text-subtext">
                    Học chưa đủ
                  </span>
                </div>
                <span className="text-base font-medium text-body">
                  {totalNotEnoughDays}/{totalPlanDays}
                </span>
              </>
            )}
            {totalMissingDays > 0 && (
              <>
                <div className="flex gap-2 min-w-[130px] items-center">
                  <div className="w-6 h-6 flex items-center justify-center rounded-[4px] bg-[#ffecec] text-red-900 text-xs font-medium">
                    <img src="/svg/study-plan/x.png" />
                  </div>
                  <span className="text-base font-semibold text-subtext">
                    Bỏ lỡ
                  </span>
                </div>
                <span className="text-base font-medium text-body">
                  {totalMissingDays}/{totalPlanDays}
                </span>
              </>
            )}
            {totalFutureDays > 0 && (
              <>
                <div className="flex gap-2 min-w-[130px] items-center">
                  <div className="w-6 h-6 flex items-center justify-center rounded-[4px] bg-gray-300">
                    <SVG src="/svg/study-plan/plan.svg" />
                  </div>
                  <span className="text-base font-semibold text-subtext">
                    Chưa đến
                  </span>
                </div>
                <span className="text-base font-medium text-body">
                  {totalFutureDays}/{totalPlanDays}
                </span>
              </>
            )}
          </div>
          <div className="h-0 w-full border-b border-gray-400 border-dashed"></div>
          <div className="flex flex-col gap-6">
            {selectedDate && (
              <div className="flex flex-col gap-2 text-sm text-gray-700">
                <p className="font-semibold text-[18px] leading-[100%] tracking-[0%] font-inter text-blue-700">
                  {
                    weekdaysMap2[
                      (1 <<
                        (getDay(selectedDate) + 6) %
                          7) as keyof typeof weekdaysMap2
                    ]
                  }
                  {', '}
                  {format(selectedDate, 'dd/MM/yyyy')}
                </p>
                {renderDayInfo()}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PlanCalendar;
