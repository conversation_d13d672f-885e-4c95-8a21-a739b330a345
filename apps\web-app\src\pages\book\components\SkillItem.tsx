import React, { useMemo, useState } from 'react';
import SVG from 'react-inlinesvg';
import { useNavigate } from 'react-router';
import ActivateBookPaidModal from '@/components/activate-book-paid-modal';
import ActivateBookFreeModal from '@/components/activate-book-free-modal';
import {
  CheckpointCacheStatus,
  getTagSkillHLTM,
  SkillLevel,
  SkillStatusPractice,
  SkillType,
  TagSkillHLTM,
} from '@/helpers/types';
import { Skill, Section, useBookContext } from '../BookContext';
import { checkBookNotFree } from '@/pages/book/helper';
import { checkStatusSkill } from '@/utils/skill';
import { useAppContext } from '@/providers/context/AppContext';

type Props = {
  skill: Skill;
  section: Section;
  index: number;
};

const SkillItem: React.FC<Props> = ({ skill, index, section }) => {
  const user = useAppContext().appConfig.config.user;
  const { isActivated, book, setBook } = useBookContext();
  const isPaidBook = book ? checkBookNotFree(book.kindOfBook) : false;
  const navigate = useNavigate();
  const [showActivatePaidModal, setShowActivatePaidModal] = useState(false);
  const [showActivateFreeModal, setShowActivateFreeModal] = useState(false);

  const handleClickSkill = (e: React.MouseEvent) => {
    e.preventDefault();

    if (skill.type === SkillType.Checkpoint) {
      if (!isActivated) {
        if (isPaidBook || !user) {
          setShowActivatePaidModal(true);
        } else {
          setShowActivateFreeModal(true);
        }
      } else {
        navigate(`/checkpoints/${skill.id}?lessonId=${section.lessonId}`); // chưa xử lý trường hợp link đến bài kiểm tra
      }
    } else {
      navigate(`/skill/${skill.id}?lessonId=${section.lessonId}`);
    }
  };

  const tagSkill = useMemo(() => {
    let tag = TagSkillHLTM.Normal;
    switch (true) {
      case skill.type === SkillType.Checkpoint:
        tag = TagSkillHLTM.Checkpoint;
        break;
      case skill.level === SkillLevel.Difficult:
        tag = TagSkillHLTM.DifficultSkill;
        break;
    }
    return getTagSkillHLTM[tag];
  }, [skill]);

  const colorStatus = useMemo(() => {
    if (!user) {
      if (skill.type === SkillType.Checkpoint) return 'text-gray-500';
      return 'text-gray-300';
    }

    if (skill.type === SkillType.Checkpoint) {
      const listStatus = skill.checkpoint?.listCheckpointCacheStatus || [];
      if (listStatus.length === 0) return 'text-gray-500';
      if (listStatus.every((item) => item === CheckpointCacheStatus.Finish)) {
        return 'text-success';
      } else if (
        listStatus.some((item) => item === CheckpointCacheStatus.Doing)
      ) {
        return 'text-warning';
      } else {
        return 'text-gray-500';
      }
    } else {
      switch (checkStatusSkill(skill?.skillResults)) {
        case SkillStatusPractice.Finish:
          return 'text-success';
        case SkillStatusPractice.Doing:
          return 'text-warning';
        case SkillStatusPractice.NotPractice:
          return 'text-gray-300';
      }
    }
  }, [skill, user]);

  return (
    <React.Fragment>
      <button
        onClick={handleClickSkill}
        className="w-full text-left p-4 not-last:border-b border-border inline-flex text-base text-body cursor-pointer"
      >
        <span>
          <SVG
            className={`mr-2 translate-y-[-2px] ${colorStatus}`}
            width={24}
            height={24}
            src={
              tagSkill.type === TagSkillHLTM.Checkpoint
                ? tagSkill.icon
                : getTagSkillHLTM[TagSkillHLTM.Normal].icon
            }
          />
        </span>
        <div className="min-w-8 font-semibold">{index + 1}.</div>
        <div className="inline-block align-middle font-medium gap-2">
          <span className="mr-2">{`${skill.name}`}</span>
          {tagSkill.type === TagSkillHLTM.DifficultSkill && (
            <SVG
              className="inline-block mr-1 translate-y-[-2px]"
              width={24}
              height={24}
              src={tagSkill.icon}
            />
          )}
          {user &&
            skill.skillResults
              ?.filter((sr) => sr.scores >= 100)
              ?.map((_, index) =>
                index < 3 ? (
                  <SVG
                    key={`${skill.id}-${index}`}
                    className="inline-block mr-0.5"
                    src={'/svg/book/gold-badge.svg'}
                  />
                ) : (
                  <></>
                ),
              )}
          {user && skill.skillResults?.[0] && (
            <span className="inline-block text-gray-600">
              {`(${skill.skillResults?.[0].scores})`}
            </span>
          )}
        </div>
      </button>
      <ActivateBookPaidModal
        show={showActivatePaidModal}
        setShow={setShowActivatePaidModal}
      />
      <ActivateBookFreeModal
        show={showActivateFreeModal}
        setShow={setShowActivateFreeModal}
        bookId={book?.id || ''}
        bookName={book?.name || ''}
        onSuccess={() => {
          setBook((prev) => (prev ? { ...prev, isFavorite: true } : prev));
        }}
      />
    </React.Fragment>
  );
};

export default SkillItem;
