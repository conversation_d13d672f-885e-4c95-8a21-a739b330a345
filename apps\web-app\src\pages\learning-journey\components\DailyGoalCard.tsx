import {
  useGetCurrentStudyPlan,
  useGetDaysStatus,
} from '@/api/study-plan/queries';
import { StudyPlan } from '@/pages/study-plan/helper';
import { Button } from '@workspace/ui/components/button';
import { ArrowRight, ChevronRight } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import { useNavigate } from 'react-router';
import SVG from 'react-inlinesvg';
import { createRoundedArcPath } from '../helper';
import { format, isBefore, startOfDay } from 'date-fns';
import { cn } from '@/lib/utils';
import UpsertStudyPlanModal from '@/pages/study-plan/components/UpsertStudyPlanModal';

const DailyGoalCard = () => {
  const navigate = useNavigate();
  const [studyPlan, setStudyPlan] = useState<StudyPlan>();
  const [animatedAngle, setAnimatedAngle] = useState(-90);
  const { data: studyPlansData, refetch } = useGetCurrentStudyPlan();
  const [timeStudy, setTimeStudy] = useState(0);
  const [open, setOpen] = useState(false);
  const [newPlan, setNewPlan] = useState<StudyPlan>({} as StudyPlan);
  const { data: daysData } = useGetDaysStatus(
    studyPlan?.id || '',
    format(new Date(), 'yyyy-MM-dd'),
    format(new Date(), 'yyyy-MM-dd'),
  );
  const progressRatio = studyPlan
    ? Math.min(1, timeStudy / studyPlan?.duration)
    : 0;
  const currentAngle = useMemo(
    () => -90 + 180 * progressRatio,
    [progressRatio],
  );

  useEffect(() => {
    if (studyPlansData) {
      setStudyPlan(studyPlansData.data.data);
    }
  }, [studyPlansData]);

  useEffect(() => {
    if (daysData) {
      setTimeStudy(daysData?.data?.data?.at(0)?.timeStudy / 60000 || 0);
    }
  }, [daysData]);

  useEffect(() => {
    const targetAngle = 180 * progressRatio;
    const duration = 1000;
    const startTime = Date.now();
    const startAngle = -90;

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);

      const easedProgress = 1 - Math.pow(1 - progress, 3);

      const currentAngle =
        startAngle + (targetAngle - startAngle) * easedProgress;
      setAnimatedAngle(currentAngle);

      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };

    requestAnimationFrame(animate);
  }, [progressRatio]);

  useEffect(() => {
    const style = document.createElement('style');
    style.innerHTML = `
    @keyframes clockHandSpin {
      0% {
        transform: rotate(-90deg);
      }
      100% {
        transform: rotate(${currentAngle}deg);
      }
    }

    .clock-hand-animated {
      animation-fill-mode: forwards;
    }
  `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, [currentAngle]);

  const hasGoalToday = useMemo(() => {
    if (!studyPlan) return false;
    const weekday = 1 << (new Date().getDay() + 6) % 7;
    return (
      (studyPlan.weekdays & weekday) !== 0 &&
      !isBefore(
        startOfDay(new Date()),
        startOfDay(new Date(studyPlan.startDate)),
      )
    );
  }, [studyPlan]);

  const d1 = createRoundedArcPath({
    cx: 60,
    cy: 60,
    outerRadius: 60,
    innerRadius: 36,
    startAngle: -90,
    endAngle: 90,
    cornerRadius: 4,
  });

  const d2 = createRoundedArcPath({
    cx: 60,
    cy: 60,
    outerRadius: 60,
    innerRadius: 36,
    startAngle: -90,
    endAngle: 180 * progressRatio - 90,
    cornerRadius: 4,
  });

  if (!hasGoalToday && studyPlan) return null;

  return (
    <div className="flex flex-col gap-4 rounded-md border border-cyan shadow-[0px_2px_0px_0px_#00A4FF] bg-white">
      <div className="flex items-center justify-between rounded-t-md bg-cyan-100 px-3 py-2">
        <div className="text-lg font-semibold text-title flex items-center gap-2">
          <SVG src="/svg/learning-journey/target.svg" className="w-6 h-6" />
          {studyPlan ? 'Mục tiêu tự học hôm nay' : 'Mục tiêu tự học'}
        </div>
        <Button
          variant="link"
          className="text-primary"
          onClick={() => navigate('/ke-hoach-hoc-tap')}
        >
          <span className="ml-1 text-sm"> Xem thêm</span>
          <ChevronRight className="w-5 h-5" />
        </Button>
      </div>

      {studyPlan ? (
        <div className="p-6 gap-8 flex flex-col items-center">
          {progressRatio === 1 && (
            <span className="flex gap-3 text-warning text-2xl font-bold items-center">
              <img
                src="/images/learning-journey/fireworks.png"
                className="w-10 h-10"
              />
              Hoàn thành xuất sắc!
            </span>
          )}
          <div className="flex gap-4">
            <div className="flex justify-center">
              <svg width={120} height={70} viewBox="0 0 120 70">
                <defs>
                  <linearGradient
                    id="bgGradient"
                    x1="0%"
                    y1="0%"
                    x2="100%"
                    y2="0%"
                  >
                    <stop offset="0%" stopColor="#E43737" />
                    <stop offset="100%" stopColor="#C72121" />
                  </linearGradient>
                  <linearGradient
                    id="progressGradient"
                    x1="0%"
                    y1="0%"
                    x2="100%"
                    y2="0%"
                  >
                    <stop offset="0%" stopColor="#E3C638" />
                    <stop offset="100%" stopColor="#C77721" />
                  </linearGradient>
                  <linearGradient
                    id="doneGradient"
                    x1="0%"
                    y1="0%"
                    x2="100%"
                    y2="0%"
                  >
                    <stop offset="0%" stopColor="#0FBC6B" />
                    <stop offset="100%" stopColor="#169559" />
                  </linearGradient>
                </defs>
                <path d={d1} fill="url(#bgGradient)" />
                <path
                  d={d2}
                  fill={`url(#${progressRatio === 1 ? 'doneGradient' : 'progressGradient'})`}
                />
                <g transform={`rotate(${animatedAngle}, 60, 60)`}>
                  <g transform="translate(60, 60) scale(0.8) translate()">
                    <path
                      d="M1.33289 61.1836L52.2091 66.1448C54.0503 68.5344 56.938 70.0762 60.1894 70.0762C65.7522 70.0762 70.2656 65.5684 70.2656 60.0028C70.2656 54.4373 65.7578 49.9266 60.1922 49.9238C56.9408 49.9238 54.0531 51.4656 52.2091 53.8552L1.33289 58.7968C-0.110963 58.9367 -0.110963 61.0437 1.33289 61.1836ZM60.1922 55.3439C62.7638 55.3439 64.8512 57.4313 64.8484 60.0028C64.8484 62.5743 62.761 64.6617 60.1894 64.659C57.6179 64.659 55.5305 62.5715 55.5333 60C55.5333 57.4285 57.6207 55.3411 60.1922 55.3439Z"
                      fill="#3F4254"
                    />
                  </g>
                </g>
              </svg>
            </div>
            <div className="flex flex-col">
              <span className="text-subtext text-sm font-medium">Đã đạt</span>
              <span className="text-xl font-bold text-subtext">
                <span
                  className={cn('text-[30px] leading-[1.5]', {
                    'text-danger': timeStudy === 0,
                    'text-warning':
                      timeStudy > 0 && timeStudy < studyPlan.duration,
                    'text-success': timeStudy >= studyPlan.duration,
                  })}
                >
                  {Math.ceil(timeStudy)}
                </span>{' '}
                / {studyPlan.duration} phút học
              </span>
            </div>
          </div>
          {timeStudy < studyPlan.duration && (
            <Button
              variant="primary"
              className="w-fit"
              onClick={() => navigate(`/thu-vien/on-luyen`)}
            >
              <span className="px-1">
                {timeStudy > 0 ? 'Tiếp tục học' : 'Vào học'}
              </span>
              <ArrowRight className="w-5 h-5" />
            </Button>
          )}
        </div>
      ) : (
        <div className="flex flex-col p-6 gap-3 items-center">
          <SVG
            src="/svg/learning-journey/no-plan.svg"
            className="w-[210px] aspect-[1.5]"
          />
          <span className="text-center text-body text-base font-medium">
            Bạn chưa đặt mục tiêu học hàng ngày.
            <br />
            Hãy đặt mục tiêu để bắt đầu hành trình nhé!
          </span>
          <Button
            variant="success"
            className="w-fit"
            onClick={() => setOpen(true)}
          >
            <span className="px-1">Tạo mục tiêu</span>
            <ArrowRight className="w-5 h-5" />
          </Button>
          <UpsertStudyPlanModal
            open={open}
            setOpen={setOpen}
            initialData={newPlan}
            setStudyPlan={setNewPlan}
            onSuccess={() => {
              refetch();
              navigate(`/ke-hoach-hoc-tap`);
            }}
          />
        </div>
      )}
    </div>
  );
};

export default DailyGoalCard;
