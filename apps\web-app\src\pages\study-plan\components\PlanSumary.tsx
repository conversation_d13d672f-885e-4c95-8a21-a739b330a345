import { StudyPlan, Weekdays, missingDays } from '../helper';

import { Calendar, Clock, CalendarX } from 'lucide-react';
import { BsSticky } from 'react-icons/bs';
import { TbClockCheck } from 'react-icons/tb';
import { format, parse, parseISO } from 'date-fns';

interface Props {
  studyPlan: StudyPlan;
}

const PlanSumary = ({ studyPlan }: Props) => {
  return (
    <div className="p-4 flex flex-col gap-3 w-full xl:flex-1 border border-border rounded-md shadow-small">
      <span className="text-base leading-[1.3] text-title font-semibold">
        Thông tin chung
      </span>
      <div className="grid grid-cols-1 sm:grid-cols-[auto_1fr] gap-y-2 sm:gap-y-1 gap-x-3 text-base text-body font-medium">
        <div className="flex gap-2 min-w-0 w-full sm:w-fit h-auto sm:h-[38px] items-center">
          <Clock className="w-5 h-5 text-gray-700 flex-shrink-0" />
          <span className="text-subtext font-semibold">Thời lượng</span>
        </div>
        <span className="sm:align-middle sm:leading-[38px] break-words">
          {`${studyPlan.duration} phút / ngày`}
        </span>
        {studyPlan.startTime && (
          <>
            <div className="flex gap-2 min-w-0 w-full sm:w-fit h-auto sm:h-[38px] items-center">
              <TbClockCheck className="w-5 h-5 text-gray-700 flex-shrink-0" />
              <span className="text-subtext font-semibold">
                Giờ học yêu thích
              </span>
            </div>
            <span className="sm:align-middle sm:leading-[38px] break-words">
              {`${format(parse(studyPlan.startTime, 'HH:mm:ss', new Date()), 'HH:mm')}`}
            </span>
          </>
        )}
        {studyPlan.weekdays !== Weekdays.All && (
          <>
            <div className="flex gap-2 min-w-0 w-full sm:w-fit h-auto sm:h-[38px] items-center">
              <CalendarX className="w-5 h-5 text-gray-700 flex-shrink-0" />
              <span className="text-subtext font-semibold">Ngày không học</span>
            </div>
            <span className="sm:align-middle sm:leading-[38px] break-words">
              {`${missingDays(studyPlan.weekdays).join(', ')}`}
            </span>
          </>
        )}
        <div className="flex gap-2 min-w-0 w-full sm:w-fit h-auto sm:h-[38px] items-center">
          <Calendar className="w-5 h-5 text-gray-700 flex-shrink-0" />
          <span className="text-subtext font-semibold">Thời gian áp dụng</span>
        </div>
        <span className="sm:align-middle sm:leading-[38px] break-words">
          {`${!studyPlan.endDate ? 'Từ ngày ' : ''}${format(parseISO(studyPlan.startDate), 'dd/MM/yyyy')}${
            studyPlan.endDate
              ? ` - ${format(parseISO(studyPlan.endDate), 'dd/MM/yyyy')}`
              : ''
          }`}
        </span>
        <div className="flex gap-2 min-w-0 w-full sm:w-fit h-auto sm:h-[38px] items-center">
          <BsSticky className="w-5 h-5 text-gray-700 flex-shrink-0" />
          <span className="text-subtext font-semibold">Ghi chú</span>
        </div>
        <span
          className="sm:align-middle pt-2 break-words"
          dangerouslySetInnerHTML={{
            __html:
              studyPlan.note && studyPlan.note.trim()
                ? studyPlan.note.replace(/\n/g, '<br/>')
                : '-',
          }}
        ></span>
      </div>
    </div>
  );
};

export default PlanSumary;
