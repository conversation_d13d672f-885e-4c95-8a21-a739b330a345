import SVG from 'react-inlinesvg';
import { Button } from '@workspace/ui/components/button';
import { useGetEndedStudyPlans } from '@/api/study-plan/queries';
import { useState } from 'react';
type Props = {
  setOpenCreateModal: React.Dispatch<React.SetStateAction<boolean>>;
};

const NoStudyPlan = ({ setOpenCreateModal }: Props) => {
  const { data: endedStudyPlans } = useGetEndedStudyPlans();
  const [hovered, setHovered] = useState(false);
  return (
    <div className="w-full min-h-screen flex justify-center">
      <div className="container p-4 sm:p-6 pb-0 flex flex-col gap-3 max-w-full">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <h2 className="text-xl sm:text-2xl font-semibold leading-[1.2] break-words">
            <PERSON><PERSON> hoạch học tập
          </h2>
          {endedStudyPlans?.data?.data?.length > 0 && (
            <Button
              variant="outline-white"
              onClick={() => {}}
              onMouseEnter={() => setHovered(true)}
              onMouseLeave={() => setHovered(false)}
              className="relative min-w-[160px] sm:min-w-[200px] text-sm sm:text-base"
            >
              <SVG
                src="/svg/study-plan/rotate-clock.svg"
                className="w-4 h-4 sm:w-5 sm:h-5 flex-shrink-0"
              />
              <span className="px-1 relative min-w-0">
                <span
                  className={`transition-opacity duration-200 ${hovered ? 'opacity-0' : 'opacity-100'} break-words`}
                >
                  Xem mục tiêu đã kết thúc
                </span>
                <span
                  className={`absolute inset-0 transition-opacity duration-200 ${hovered ? 'opacity-100' : 'opacity-0'} break-words`}
                >
                  Sắp ra mắt
                </span>
              </span>
            </Button>
          )}
        </div>
        <div className="flex flex-col items-center gap-3 pt-10 sm:pt-20 px-4">
          <img
            src="/illustrations/no-study-plan.png"
            className="w-full max-w-[300px] h-auto"
            alt="No study plan illustration"
          />
          <span className="font-medium text-sm sm:text-base py-[11px] text-center text-body max-w-md">
            Bạn chưa đặt mục tiêu học hàng ngày.
            <br />
            Hãy đặt mục tiêu để bắt đầu hành trình nhé!
          </span>
          <Button
            variant="success"
            onClick={() => setOpenCreateModal(true)}
            className="text-sm sm:text-base"
          >
            <span className="font-bold px-1">Tạo mục tiêu</span>
            <SVG
              src="/svg/arrow-right.svg"
              className="w-4 h-4 sm:w-5 sm:h-5 flex-shrink-0"
            />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default NoStudyPlan;
