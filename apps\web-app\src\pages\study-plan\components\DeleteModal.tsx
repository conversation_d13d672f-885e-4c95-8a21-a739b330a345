import { Button } from '@workspace/ui/components/button';
import {
  <PERSON><PERSON>,
  DialogBody,
  DialogContent,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from '@workspace/ui/components/dialog';
import { useState } from 'react';
import { StudyPlan } from '../helper';
import { deleteStudyPlan } from '@/api/study-plan/fetchers';
import { toast } from 'react-toastify';

type Props = {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  studyPlan: StudyPlan;
  setStudyPlan: React.Dispatch<React.SetStateAction<StudyPlan>>;
  setOpenStopModal: React.Dispatch<React.SetStateAction<boolean>>;
};

function DeleteModal({
  open,
  setOpen,
  studyPlan,
  setStudyPlan,
  setOpenStopModal,
}: Props) {
  const [isLoading, setIsLoading] = useState(false);
  const handleConfirm = async () => {
    try {
      setIsLoading(true);
      const id = studyPlan?.id;
      if (!id) return;
      const res = await deleteStudyPlan(id);
      if (res.status === 200) {
        setOpen(false);
        setStudyPlan({} as StudyPlan);
        toast.success('Đã xoá mục tiêu!');
      }
    } finally {
      setIsLoading(false);
    }
  };
  const handleClickStop = () => {
    setOpen(false);
    setOpenStopModal(true);
  };
  const handleCancel = () => {
    setOpen(false);
    setIsLoading(false);
  };

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      handleCancel();
    }
  };
  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-150 max-h-[80vh] h-fit flex flex-col mx-4">
        <DialogHeader>
          <DialogTitle className="text-lg sm:text-xl font-semibold">
            Xóa mục tiêu hàng ngày
          </DialogTitle>
        </DialogHeader>
        <DialogBody className="font-medium text-sm sm:text-base text-body flex flex-col gap-4 flex-1 h-fit overflow-auto hide-scrollbar">
          <span>
            Bạn có chắc chắn muốn xóa mục tiêu này? Sau khi xoá, bạn sẽ:
          </span>
          <div className="flex rounded-md px-3 sm:px-4 py-3 gap-2 bg-pink-100 border border-danger">
            <ul className="list-disc pl-4">
              <li className="text-sm sm:text-base text-body font-medium leading-[1.5]">
                Mất toàn bộ lịch học đã tạo
              </li>
              <li className="text-sm sm:text-base text-body font-medium leading-[1.5]">
                Mất toàn bộ kết quả học theo mục tiêu.
              </li>
              <li className="text-sm sm:text-base text-body font-medium leading-[1.5]">
                Không thể xem lại mục tiêu.
              </li>
            </ul>
          </div>
          <span>
            Nếu không muốn tiếp tục học, bạn có thể thực hiện hành động khác
            thay vì xoá:
            <strong className="text-primary"> Kết thúc mục tiêu</strong>.
          </span>
        </DialogBody>
        <DialogFooter className="flex-col sm:flex-row gap-2">
          <Button
            variant="light"
            onClick={handleCancel}
            type="button"
            className="w-full sm:w-auto"
          >
            Hủy
          </Button>
          <Button onClick={handleClickStop} className="w-full sm:w-auto">
            Kết thúc mục tiêu
          </Button>
          <Button
            variant="danger"
            onClick={handleConfirm}
            disabled={isLoading}
            className="w-full sm:w-auto"
          >
            Xóa
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default DeleteModal;
