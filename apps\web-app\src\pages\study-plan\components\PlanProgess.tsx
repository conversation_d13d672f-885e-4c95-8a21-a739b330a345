import { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON>, Pie, Cell, Tooltip } from 'recharts';
import { useGetDayElapsed } from '@/api/study-plan/queries';
import { StudyPlan } from '../helper';
import { useMinMediumScreen } from '@/hooks/UseMediaScreen';
type Props = {
  studyPlan: StudyPlan;
};

const COLORS = ['#15A662', '#EAAA08', '#E75F5F'];
const CHART_WIDTH = 110;
const CHART_HEIGHT = 110;
const CHART_RADIUS = {
  inner: 42,
  outer: 55,
};

const PlanProgress = ({ studyPlan }: Props) => {
  const isMediumScreen = useMinMediumScreen();
  const { data } = useGetDayElapsed(studyPlan.id || '');
  const [dayElapsedData, setDayElapsedData] = useState<{
    doneDayCount: number;
    notEnoughDayCount: number;
    missingDayCount: number;
    totalDayCount: number;
  } | null>(null);

  useEffect(() => {
    if (data?.data) {
      setDayElapsedData(data.data?.data);
    }
  }, [data]);

  if (!dayElapsedData) return null;

  const { doneDayCount, notEnoughDayCount, missingDayCount, totalDayCount } =
    dayElapsedData;

  const chartData = [
    { name: 'Hoàn thành', value: doneDayCount },
    { name: 'Học chưa đủ', value: notEnoughDayCount },
    { name: 'Bỏ lỡ', value: missingDayCount },
  ];

  if (dayElapsedData.totalDayCount == 0) return;

  return (
    <div
      className={`p-4 ${isMediumScreen ? 'w-[264px]' : 'w-full'} rounded-xl border text-center shadow-sm`}
    >
      <p className="font-semibold text-base mb-2">
        Số ngày học đã qua: {totalDayCount}
      </p>
      <div className="relative flex justify-center items-center">
        <div className="relative overflow-visible">
          <PieChart width={CHART_WIDTH} height={CHART_HEIGHT}>
            <Pie
              data={chartData}
              cx="50%"
              cy="50%"
              innerRadius={CHART_RADIUS.inner}
              outerRadius={CHART_RADIUS.outer}
              paddingAngle={2}
              dataKey="value"
              startAngle={90}
              endAngle={-270}
            >
              {chartData.map((_, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={COLORS[index % COLORS.length]}
                />
              ))}
            </Pie>

            <Tooltip
              content={({ active, payload, coordinate }) => {
                if (active && payload && payload.length && coordinate) {
                  const { name, value } = payload[0];
                  const safeValue = typeof value === 'number' ? value : 0;
                  const percent = ((safeValue / totalDayCount) * 100).toFixed(
                    1,
                  );

                  const { x, y } = coordinate ?? {};
                  if (typeof x !== 'number' || typeof y !== 'number')
                    return null;

                  const segmentIndex = chartData.findIndex(
                    (item) => item.name === name,
                  );
                  const segmentColor = COLORS[segmentIndex] ?? '#000';

                  const centerX = CHART_WIDTH / 2;
                  const centerY = CHART_HEIGHT / 2;

                  const angle = Math.atan2(y - centerY, x - centerX);

                  const distance = CHART_RADIUS.outer + 12;

                  const tooltipX = centerX + Math.cos(angle) * distance;
                  const tooltipY = centerY + Math.sin(angle) * distance;

                  const tooltipWidth = 60;
                  const tooltipHeight = 30;

                  const adjustedX = Math.max(
                    0,
                    Math.min(
                      tooltipX - tooltipWidth / 2,
                      CHART_WIDTH - tooltipWidth,
                    ),
                  );
                  const adjustedY = Math.max(
                    0,
                    Math.min(
                      tooltipY - tooltipHeight / 2,
                      CHART_HEIGHT - tooltipHeight,
                    ),
                  );

                  return (
                    <div
                      className="absolute z-50 px-2 py-1 rounded-[4px] shadow-lg text-white text-sm font-medium pointer-events-none text-center"
                      style={{
                        top: adjustedY,
                        left: adjustedX,
                        backgroundColor: segmentColor,
                        width: tooltipWidth,
                      }}
                    >
                      {percent}%
                    </div>
                  );
                }
                return null;
              }}
            />
          </PieChart>
        </div>

        <div className="absolute top-[50%] left-[50%] translate-x-[-50%] translate-y-[-50%]">
          <p className="text-xl font-semibold text-title">{totalDayCount}</p>
          <p className="text-sm font-semibold text-placeholder">Ngày học</p>
        </div>
      </div>
      <div className="mt-4 text-left space-y-2 font-semibold">
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 rounded-full bg-[#15A662]" />
          <span className="text-sm text-gray-600">Hoàn thành</span>
          <span className="ml-auto text-sm text-gray-500">
            <span className="text-title text-lg">{doneDayCount}</span>/
            {totalDayCount}
          </span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 rounded-full bg-[#EAAA08]" />
          <span className="text-sm text-gray-600">Học chưa đủ</span>
          <span className="ml-auto text-sm text-gray-500">
            <span className="text-title text-lg">{notEnoughDayCount}</span>/
            {totalDayCount}
          </span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 rounded-full bg-[#E75F5F]" />
          <span className="text-sm text-gray-600">Bỏ lỡ</span>
          <span className="ml-auto text-sm text-gray-500">
            <span className="text-title text-lg">{missingDayCount}</span>/
            {totalDayCount}
          </span>
        </div>
      </div>
    </div>
  );
};

export default PlanProgress;
