import { newDateFilterOptions } from '@/helpers/mission';
import {
  FilterDataMissions,
  MissionData,
  SortType,
} from '@/pages/mission/helper';
import MissionFilterBar from '@/pages/mission/component/FilterBar/MissionFilterBar';
import { Button } from '@workspace/ui/components/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@workspace/ui/components/table';
import { useEffect, useMemo, useState } from 'react';
import { FaSort, FaSortDown, FaSortUp } from 'react-icons/fa6';
import { useLocation, useNavigate } from 'react-router';
import {
  SortOptionTask,
  StatusSuggestionStudent,
  dataHeaderTaskStudent,
} from '@/helpers/mission';
import { ESuggestionMode } from '@/helpers/types';
import { useAppContext } from '@/providers/context/AppContext';
import SVG from 'react-inlinesvg';
import moment from 'moment';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@workspace/ui/components/pagination';
import ViewInfoMission from '@/pages/mission/component/ViewInfoMission/ViewInfoMission';
import { cn } from '@workspace/ui/lib/utils';
import Splash from '@/components/splash/Splash';
import { getPaginationRange } from '@/utils/pagination';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@workspace/ui/components/tooltip';
import { useSuggestionInfoStudent } from '@/api/mission/queries';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@workspace/ui/components/select';

import { useGetTenantForUser } from '@/api/tenant/queries';
import {
  useGetAllClassroom,
  useGetAllSuggestion,
} from '@/api/tenantmission/queries';
import NoneMissionPage from './NoneMissionPage';
import NoneClassPage from './NoneClassPage';

const Mission = () => {
  const [selectedClassroomId, setSelectedClassroomId] = useState<
    string | undefined | null
  >(null);
  const [selectedTenantId, setSelectedTenantId] = useState<number | undefined>(
    undefined,
  );
  const { data: listTenant } = useGetTenantForUser();
  const tenants = listTenant?.data?.data ?? [];
  const selectedTenantCode = tenants.find(
    (t) => t.id === selectedTenantId,
  )?.code;

  const { data: listClassroom } = useGetAllClassroom(selectedTenantCode);
  const classrooms = listClassroom?.data?.data ?? [];

  const navigate = useNavigate();
  const location = useLocation();
  const subjects = useAppContext().appConfig.config?.subjects;
  const userId = useAppContext().appConfig.config?.user.id;
  const initialfilterDatas: FilterDataMissions = {
    tenantId: -1,
    name: '',
    gradeId: undefined,
    skipCount: 0,
    maxResultCount: 10,
    sorter: '',
    sortOrder: SortType.None,
    fromDate: newDateFilterOptions[0].fromDate,
    toDate: newDateFilterOptions[0].toDate,
    dateType: newDateFilterOptions[0].type,
    subjectIds: [],
    modes: [],
  };
  const [filterDatas, setFilterDatas] =
    useState<FilterDataMissions>(initialfilterDatas);

  useEffect(() => {
    if (selectedTenantId !== undefined) {
      localStorage.setItem('selectedTenantId', selectedTenantId.toString());
      setFilterDatas((prev) => ({
        ...prev,
        tenantId: selectedTenantId,
      }));
    }
  }, [selectedTenantId]);

  const [infoModal, setInfoModal] = useState<{
    show: boolean;
    loading: boolean;
    data: MissionData | undefined;
  }>({
    show: false,
    loading: false,
    data: undefined,
  });
  const [selected, setSelected] = useState<MissionData | undefined>(undefined);

  const {
    data: suggestionData,
    isFetching,
    refetch,
  } = useGetAllSuggestion(filterDatas, selectedClassroomId || undefined);

  const isLoading = isFetching;
  const dataSource = suggestionData;

  const data = useMemo(
    () => ({
      items: dataSource?.data?.items ?? [],
      totalItem: dataSource?.data?.total ?? 0,
      dataDynamic: dataSource?.data?.dataDynamic,
    }),
    [dataSource],
  );

  const {
    data: detailSuggestionDataRoleStudent,
    isLoading: isLoadingDetailSuggestionRoleStudent,
  } = useSuggestionInfoStudent(selected?.id ?? '', true);

  const renderSortStatus = (
    sortType: SortType,
    sort: SortOptionTask | undefined,
  ) => {
    const type = sort === filterDatas.sorter ? sortType : SortType.None;
    switch (type) {
      case SortType.Ascending:
        return (
          <FaSortDown
            className="w-[8px] text-gray"
            onClick={() =>
              setFilterDatas({
                ...filterDatas,
                tenantId: selectedTenantId!,
                sortOrder: SortType.Descending,
                sorter: sort,
              })
            }
          />
        );
      case SortType.Descending:
        return (
          <FaSortUp
            className="w-[8px] text-gray"
            onClick={() =>
              setFilterDatas({
                ...filterDatas,
                tenantId: selectedTenantId!,
                sortOrder: SortType.None,
                sorter: sort,
              })
            }
          />
        );
      case SortType.None:
        return (
          <FaSort
            className="w-[8px] text-gray"
            onClick={() =>
              setFilterDatas({
                ...filterDatas,
                tenantId: selectedTenantId!,
                sortOrder: SortType.Ascending,
                sorter: sort,
              })
            }
          />
        );
    }
  };

  const handleClickTask = (data: MissionData) => {
    setSelected(data);
    setInfoModal({
      show: true,
      loading: true,
      data: data,
    });
  };

  const dataHeader = dataHeaderTaskStudent;

  const handleActionStudent = (
    type: StatusSuggestionStudent,
    id: string,
    mode: ESuggestionMode = ESuggestionMode.Exam,
    skillId: string = '',
  ) => {
    if (
      type === StatusSuggestionStudent.NotDo ||
      type === StatusSuggestionStudent.Doing ||
      type === StatusSuggestionStudent.DoAgain
    )
      if (mode == ESuggestionMode.Exercise)
        navigate(`/skill/${skillId}?suggestionId=${id}`, {
          relative: 'path',
          state: {
            classroomId: selectedClassroomId,
          },
        });
  };

  useEffect(() => {
    const savedFilter = localStorage.getItem(
      `filter-list-task-${selectedClassroomId}-${userId}`,
    );

    if (savedFilter) {
      try {
        const parsedFilter = JSON.parse(savedFilter);
        setFilterDatas((prev) => ({
          ...prev,
          ...parsedFilter,
          tenantId: selectedTenantId ?? prev.tenantId,
        }));
      } catch (e) {
        console.error('Error parsing saved filter:', e);
      }
    } else {
      setFilterDatas({
        ...initialfilterDatas,
        tenantId: selectedTenantId ?? -1,
      });
    }
  }, [selectedClassroomId, selectedTenantId]);

  const handleFilter = (value: FilterDataMissions) => {
    setFilterDatas(value);
    localStorage.setItem(
      `filter-list-task-${selectedClassroomId}-${userId}`,
      JSON.stringify({
        fromDate: value.fromDate,
        toDate: value.toDate,
        dateType: value.dateType,
        subjectIds: value.subjectIds,
        modes: value.modes,
        skipCount: value.skipCount,
      }),
    );
  };

  useEffect(() => {
    if (tenants.length > 0) {
      const savedTenantId = localStorage.getItem(`selectedTenantId-${userId}`);
      if (savedTenantId) {
        const tenantId = Number(savedTenantId);
        const tenantExists = tenants.find((t) => t.id === tenantId);
        if (tenantExists) {
          setSelectedTenantId(tenantId);
          return;
        }
      }
      const firstTenantId = tenants[0].id;
      setSelectedTenantId(firstTenantId);
      localStorage.setItem(
        `selectedTenantId-${userId}`,
        firstTenantId.toString(),
      );
    }
  }, [tenants]);

  useEffect(() => {
    if (
      detailSuggestionDataRoleStudent?.data &&
      !isLoadingDetailSuggestionRoleStudent
    ) {
      setInfoModal({
        show: true,
        loading: false,
        data: {
          ...selected,
          ...detailSuggestionDataRoleStudent.data,
        },
      });
    }
  }, [detailSuggestionDataRoleStudent, isLoadingDetailSuggestionRoleStudent]);

  useEffect(() => {
    if (
      detailSuggestionDataRoleStudent?.data &&
      !isLoadingDetailSuggestionRoleStudent
    ) {
      setInfoModal({
        show: true,
        loading: false,
        data: {
          ...selected,
          ...detailSuggestionDataRoleStudent.data,
        },
      });
    }
  }, [detailSuggestionDataRoleStudent, isLoadingDetailSuggestionRoleStudent]);

  useEffect(() => {
    if (selectedTenantId !== undefined) {
      localStorage.setItem(
        `selectedTenantId-${userId}`,
        selectedTenantId.toString(),
      );
    }
  }, [selectedTenantId]);

  useEffect(() => {
    if (classrooms.length > 0) {
      const savedClassId = localStorage.getItem(
        `selectedClassroomId-${userId}`,
      );
      const exists = classrooms.find((cls) => cls.id === savedClassId);
      if (savedClassId && exists) {
        setSelectedClassroomId(savedClassId);
      } else if (classrooms.length === 1) {
        setSelectedClassroomId(classrooms[0].id);
      } else {
        setSelectedClassroomId(undefined);
      }
    }
  }, [classrooms]);

  useEffect(() => {
    if (selectedClassroomId !== null) {
      localStorage.setItem(
        `selectedClassroomId-${userId}`,
        selectedClassroomId ?? '',
      );
    }
  }, [selectedClassroomId]);

  useEffect(() => {
    if (location.state?.refresh) {
      refetch();
      navigate(location.pathname, { replace: true, state: {} });
    }
  }, [location.state]);

  useEffect(() => {
    if (!infoModal.show) setSelected(undefined);
  }, [infoModal.show]);

  // Helper functions to determine what to render
  const renderNoneClass = () => {
    return selectedTenantId && classrooms.length === 0 && !isLoading;
  };

  // const renderNoneMission = () => {
  //   const hasActiveFilters =
  //     filterDatas.name.trim() !== '' ||
  //     (filterDatas.subjectIds && filterDatas.subjectIds.length > 0) ||
  //     (filterDatas.modes && filterDatas.modes.length > 0) ||
  //     filterDatas.dateType !== newDateFilterOptions[0].type;

  //   return (
  //     selectedTenantId &&
  //     classrooms.length > 0 &&
  //     data?.items?.length === 0 &&
  //     !isLoading &&
  //     !hasActiveFilters
  //   );
  // };

  return (
    <div className="flex flex-col pt-6 pb-3 w-full px-6 max-w-screen-xl mx-auto">
      <div className="flex items-center flex-col flex-row justify-between gap-4 w-full">
        <div className="font-semibold text-[24px] leading-[150%] tracking-[-0.01em] align-middle text-[#3F4254] whitespace-nowrap">
          Danh sách nhiệm vụ
        </div>
        <Select
          value={selectedTenantId?.toString()}
          onValueChange={(val) => setSelectedTenantId(Number(val))}
          open={tenants.length === 1 ? false : undefined}
        >
          <SelectTrigger className="w-[240px] h-[38px] cursor-pointer">
            <SelectValue placeholder="Cơ sở giáo dục" />
          </SelectTrigger>
          <SelectContent>
            {tenants.map((cls) => (
              <SelectItem key={cls.id} value={cls.id.toString()}>
                {cls.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {renderNoneClass() ? (
        <NoneClassPage />
      ) : (
        <>
          <div className="flex  gap-4 relative mt-2">
            <div className="flex items-center flex-row gap-4 w-full">
              <Select
                value={selectedClassroomId ?? 'all'}
                onValueChange={(val) => {
                  setSelectedClassroomId(val === 'all' ? undefined : val);
                }}
                open={classrooms.length === 1 ? false : undefined}
              >
                <SelectTrigger className="w-[200px] h-[38px] cursor-pointer">
                  <SelectValue placeholder="Lớp học" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem key="all" value="all">
                    Tất cả
                  </SelectItem>
                  {classrooms.map((cls) => (
                    <SelectItem key={cls.id} value={cls.id}>
                      {cls.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <MissionFilterBar
                filterDatas={filterDatas}
                setFilterDatas={handleFilter}
                subjectIds={dataSource?.data?.subjectIds || []}
              />
            </div>
          </div>
          {isLoading ? (
            <Splash />
          ) : (
            <>
              {data?.items?.length > 0 ? (
                <div>
                  <div className="mt-4 overflow-x-auto">
                    <Table
                      classNameContainer="overflow-auto"
                      className="text-foreground shadow w-full"
                    >
                      <TableHeader className="bg-blue-100 rounded-t-[8px]">
                        <TableRow className="hover:bg-blue-100">
                          {dataHeader?.map((item) => (
                            <TableHead
                              key={item.id}
                              scope="col"
                              className={`p-4 ${item.id === 1 ? 'min-w-[400px]' : 'min-w-[204px]'}`}
                            >
                              <div
                                className={`${item.isSort ? 'flex items-center gap-4 self-stretch' : ''}`}
                              >
                                <span className="text-foreground font-semibold text-base">
                                  {item.title}
                                </span>
                                {item.isSort && (
                                  <div className="flex items-center">
                                    {renderSortStatus(
                                      filterDatas.sortOrder,
                                      item.value,
                                    )}
                                  </div>
                                )}
                              </div>
                            </TableHead>
                          ))}
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {data?.items?.map((elm: MissionData) => {
                          const classroomName =
                            classrooms.find((cls) => cls.id === elm.classroomId)
                              ?.name ?? elm.classroomId;
                          return (
                            <TableRow
                              key={elm.id}
                              data-text={elm.id}
                              className={cn(
                                'cursor-pointer',
                                elm.id === selected?.id && 'bg-blue-100',
                              )}
                              onClick={() => handleClickTask(elm)}
                            >
                              <TableCell className="max-w-[50px] md:max-w-[250px] whitespace-nowrap overflow-hidden text-ellipsis p-4">
                                <div className="overflow-hidden text-ellipsis whitespace-nowrap text-body font-medium text-base">
                                  {elm.name}
                                </div>
                                <span className="flex items-center gap-2">
                                  <SVG
                                    className="min-w-[20px] min-h-[20px]"
                                    width={20}
                                    height={20}
                                    src={
                                      import.meta.env.VITE_PUBLIC_URL +
                                      '/images/logo.svg'
                                    }
                                  />
                                  <span
                                    className="truncate"
                                    title={`Ôn luyện / ${elm?.extra?.bookName} / ${elm?.extra?.chapterName}`}
                                  >
                                    / {elm?.extra?.bookName} /{' '}
                                    {elm?.extra?.chapterName}
                                  </span>
                                </span>
                              </TableCell>
                              <TableCell className="max-w-[180px] whitespace-nowrap overflow-hidden text-ellipsis p-4">
                                <TooltipProvider>
                                  <Tooltip delayDuration={300}>
                                    <TooltipTrigger asChild>
                                      <div className="overflow-hidden text-ellipsis whitespace-nowrap text-body font-medium text-base">
                                        {classroomName}
                                      </div>
                                    </TooltipTrigger>
                                    <TooltipContent side="top" align="start">
                                      <div className="overflow-hidden text-ellipsis whitespace-nowrap text-body font-medium text-base text-white">
                                        {classroomName}
                                      </div>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              </TableCell>
                              <TableCell className="p-4 max-w-[180px] whitespace-nowrap overflow-hidden text-ellipsis">
                                <TooltipProvider>
                                  <Tooltip delayDuration={300}>
                                    <TooltipTrigger asChild>
                                      <div className="overflow-hidden text-ellipsis whitespace-nowrap text-gray-600 font-medium text-base">
                                        {subjects
                                          .filter((item) =>
                                            elm.subjectIds?.includes(item.id),
                                          )
                                          ?.map((item) => item.name)
                                          ?.join(', ')}
                                      </div>
                                    </TooltipTrigger>
                                    <TooltipContent side="top" align="start">
                                      <p>
                                        {subjects
                                          .filter((item) =>
                                            elm.subjectIds?.includes(item.id),
                                          )
                                          ?.map((item) => item.name)
                                          ?.join(', ')}
                                      </p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              </TableCell>
                              <TableCell className="p-4 max-w-[180px] whitespace-nowrap overflow-hidden text-ellipsis">
                                <div className="overflow-hidden text-ellipsis whitespace-nowrap text-gray-600 font-medium text-base">
                                  {elm.deadline
                                    ? moment
                                        .utc(elm.deadline)
                                        .local()
                                        .format('DD/MM/YYYY HH:mm')
                                    : 'Không có'}
                                </div>
                              </TableCell>
                              <TableCell className="p-4 max-w-[140px] whitespace-nowrap text-ellipsis">
                                {elm.status !== StatusSuggestionStudent.Done &&
                                elm?.deadline &&
                                new Date() > new Date(elm.deadline) ? (
                                  <div className="text-sm not-italic font-bold text-subtext text-center">
                                    Hết hạn
                                  </div>
                                ) : elm.status ===
                                  StatusSuggestionStudent.NotDo ? (
                                  <div className="flex justify-center">
                                    <Button
                                      id={`btn-notdo-${elm.skillId}`}
                                      className="w-[109px] h-[32px] px-2 py-1.5 opacity-100 font-inter font-bold text-[14px] leading-[21px] tracking-normal bg-blue-600 text-white rounded-[8px]"
                                      onClick={() =>
                                        handleActionStudent(
                                          elm.status as StatusSuggestionStudent,
                                          elm.id,
                                          elm.mode as unknown as ESuggestionMode,
                                          elm.skillId,
                                        )
                                      }
                                    >
                                      Làm bài
                                    </Button>
                                  </div>
                                ) : elm.status ===
                                  StatusSuggestionStudent.Doing ? (
                                  <div className="flex justify-center">
                                    <Button
                                      id={`btn-doing-${elm.skillId}`}
                                      onClick={() =>
                                        handleActionStudent(
                                          elm.status as StatusSuggestionStudent,
                                          elm.id,
                                          elm.mode as unknown as ESuggestionMode,
                                          elm.skillId,
                                        )
                                      }
                                      className="w-[109px] h-[32px] px-2 py-1.5 opacity-100 font-inter font-bold text-[14px] leading-[21px] tracking-normal text-white bg-[#EAAA08]  rounded-[8px]"
                                    >
                                      Tiếp tục
                                    </Button>
                                  </div>
                                ) : elm.status ===
                                  StatusSuggestionStudent.WaitResult ? (
                                  <div className="flex justify-center">
                                    <Button
                                      id={`btn-waitresult-${elm.skillId}`}
                                      variant={'link'}
                                      className="w-[109px]"
                                    >
                                      Chờ kết quả
                                    </Button>
                                  </div>
                                ) : elm.status ===
                                  StatusSuggestionStudent.Done ? (
                                  <div className="flex justify-center">
                                    <Button
                                      id={`btn-done-${elm.skillId}`}
                                      variant={'success'}
                                      className="w-[109px] h-[32px] px-2 py-1.5 opacity-100 font-inter font-bold text-[14px] leading-[21px] tracking-normal text-white bg-[#15A662]  rounded-[8px]"
                                    >
                                      Xem kết quả
                                    </Button>
                                  </div>
                                ) : elm.status ===
                                  StatusSuggestionStudent.DoAgain ? (
                                  <div className="flex justify-center">
                                    <Button
                                      id={`btn-doagain-${elm.skillId}`}
                                      onClick={() =>
                                        handleActionStudent(
                                          elm.status as StatusSuggestionStudent,
                                          elm.id,
                                          elm.mode as unknown as ESuggestionMode,
                                          elm.skillId,
                                        )
                                      }
                                      variant={'secondary'}
                                      className="w-[109px] h-[32px] px-2 py-1.5 opacity-100 font-inter font-bold text-[14px] leading-[21px] tracking-normal text-white rounded-[8px]"
                                    >
                                      Luyện lại
                                    </Button>
                                  </div>
                                ) : (
                                  <></>
                                )}
                              </TableCell>
                            </TableRow>
                          );
                        })}
                      </TableBody>
                    </Table>
                    {data.totalItem > 10 && (
                      <div className="mt-5 flex justify-end relative z-[4]">
                        <div className="">
                          <Pagination>
                            <PaginationContent>
                              <PaginationItem>
                                <PaginationPrevious
                                  href="#"
                                  className={
                                    filterDatas.skipCount === 0
                                      ? 'opacity-20 cursor-no-drop'
                                      : ''
                                  }
                                  onClick={() => {
                                    if (filterDatas.skipCount === 0) return;
                                    setFilterDatas({
                                      ...filterDatas,
                                      skipCount:
                                        filterDatas.skipCount -
                                        filterDatas.maxResultCount,
                                    });
                                  }}
                                />
                              </PaginationItem>

                              {getPaginationRange(
                                filterDatas.skipCount /
                                  filterDatas.maxResultCount +
                                  1,
                                Math.ceil(
                                  data.totalItem / filterDatas.maxResultCount,
                                ),
                              ).map((page, i) => (
                                <PaginationItem key={i}>
                                  {page === '...' ? (
                                    <span className="px-2 text-sm text-muted-foreground">
                                      …
                                    </span>
                                  ) : (
                                    <PaginationLink
                                      isActive={
                                        page ===
                                        filterDatas.skipCount /
                                          filterDatas.maxResultCount +
                                          1
                                      }
                                      onClick={() => {
                                        setFilterDatas({
                                          ...filterDatas,
                                          skipCount:
                                            (Number(page) - 1) *
                                            filterDatas.maxResultCount,
                                        });
                                      }}
                                    >
                                      {page}
                                    </PaginationLink>
                                  )}
                                </PaginationItem>
                              ))}

                              <PaginationItem>
                                <PaginationNext
                                  href="#"
                                  className={
                                    filterDatas.skipCount +
                                      filterDatas.maxResultCount >=
                                    data.totalItem
                                      ? 'opacity-20 cursor-no-drop'
                                      : ''
                                  }
                                  onClick={() => {
                                    if (
                                      filterDatas.skipCount +
                                        filterDatas.maxResultCount >=
                                      data.totalItem
                                    )
                                      return;
                                    setFilterDatas({
                                      ...filterDatas,
                                      skipCount:
                                        filterDatas.skipCount +
                                        filterDatas.maxResultCount,
                                    });
                                  }}
                                />
                              </PaginationItem>
                            </PaginationContent>
                          </Pagination>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <NoneMissionPage />
              )}
            </>
          )}
        </>
      )}
      <ViewInfoMission
        infoModal={infoModal}
        handleClose={() =>
          setInfoModal({
            show: false,
            loading: false,
            data: undefined,
          })
        }
        isStudent={true}
        handleActionStudent={handleActionStudent}
      />
    </div>
  );
};

export default Mission;
