import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
  Line,
  ComposedChart,
} from 'recharts';
import { format, parseISO, startOfDay } from 'date-fns';
import { useGetDayElapsed, useGetDaysStatus } from '@/api/study-plan/queries';
import { useEffect, useMemo, useRef, useState } from 'react';
import TimeFilterDropdown from './TimeFilterDropdown';
import {
  mapJSDateToWeekdayEnum,
  StudyDayStatus,
  StudyPlan,
  weekdaysMap2,
} from '../helper';
import Splash from '@/components/splash/Splash';

interface ChartItem {
  date: string;
  fullDate: string;
  studyTime: number;
  targetTime: number;
  status: number;
}

type Props = {
  studyPlan: StudyPlan;
};

const PlanTimeChart = ({ studyPlan }: Props) => {
  const today = new Date();
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(today.getDate() - 29);

  const [fromDate, setFromDate] = useState<Date>(() =>
    startOfDay(thirtyDaysAgo),
  );
  const [toDate, setToDate] = useState<Date>(() => startOfDay(today));
  const [daysStatus, setDaysStatus] = useState<StudyDayStatus[]>([]);
  const { data: dayElapsedData, isLoading: dayElapsedLoading } =
    useGetDayElapsed(studyPlan.id || '');
  const { data: daysData, isLoading: daysLoading } = useGetDaysStatus(
    studyPlan?.id || '',
    format(fromDate, 'yyyy-MM-dd'),
    format(toDate, 'yyyy-MM-dd'),
  );
  const containerRef = useRef<HTMLDivElement>(null);
  const [containerWidth, setContainerWidth] = useState<number>(0);
  const chartData = useMemo(() => {
    if (!daysStatus) return [];
    return daysStatus.map((item: StudyDayStatus) => {
      const date = parseISO(item.date);
      return {
        date: `${date.getDate()}/${date.getMonth() + 1}`,
        fullDate: item.date,
        studyTime: Math.round(item.timeStudy / 60000),
        targetTime: item.targetDuration,
        status: item.status,
      };
    });
  }, [daysStatus]);

  const minBarWidth = 30;
  const padding = 20;

  useEffect(() => {
    if (daysData) {
      setDaysStatus(daysData.data.data);
    }
  }, [daysData]);

  useEffect(() => {
    setTimeout(() => {
      if (containerRef.current) {
        setContainerWidth(containerRef.current.offsetWidth);
      }
    }, 0);
  }, [chartData.length]);
  const chartWidth = Math.max(
    containerWidth,
    chartData.length * (minBarWidth + padding),
  );

  const CustomTooltip = ({
    active,
    payload,
  }: {
    active?: boolean;
    payload?: ChartItem[];
  }) => {
    if (active && payload?.length) {
      const data = payload[0].payload;
      const { fullDate, studyTime, targetTime } = data;

      const date = new Date(fullDate);
      const weekdayEnum = mapJSDateToWeekdayEnum(date.getDay());
      const weekdayLabel =
        weekdaysMap2[weekdayEnum as keyof typeof weekdaysMap2];
      const formattedDate = `${String(date.getDate()).padStart(2, '0')}/${String(date.getMonth() + 1).padStart(2, '0')}/${date.getFullYear()}`;

      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-semibold text-gray-800">{`${weekdayLabel}, ${formattedDate}`}</p>
          <div className="flex items-center gap-2 mt-1">
            <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
            <span className="text-sm text-gray-600">
              Đã học: {studyTime} phút
            </span>
          </div>
          <div className="flex items-center gap-2 mt-1">
            <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
            <span className="text-sm text-gray-600">
              Mục tiêu: {targetTime} phút
            </span>
          </div>
        </div>
      );
    }
    return null;
  };

  if (dayElapsedLoading && daysLoading)
    return <Splash animated={false} logo={false} />;

  return (
    <div className="p-4 pb-3 flex flex-col gap-3 w-full border border-border rounded-md shadow-small select-none overflow-hidden">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <div className="min-w-0">
          <h2 className="text-base leading-[1.3] font-semibold text-gray-800">
            Thời gian học
          </h2>
          {dayElapsedData?.data?.data?.totalDayCount > 0 && (
            <p className="text-sm text-gray-500">
              Tổng số {chartData.reduce((sum, d) => sum + d.studyTime, 0)} phút
            </p>
          )}
        </div>
        <div className="flex-shrink-0">
          <TimeFilterDropdown
            fromDate={fromDate}
            setFromDate={setFromDate}
            toDate={toDate}
            setToDate={setToDate}
          />
        </div>
      </div>
      {dayElapsedData?.data?.data?.totalDayCount > 0 ? (
        <>
          <div
            ref={containerRef}
            className="h-60 w-full overflow-x-auto overflow-y-hidden"
          >
            <div
              style={{
                width: chartWidth,
                height: '100%',
              }}
            >
              <ResponsiveContainer width="100%" height="100%">
                <ComposedChart
                  data={chartData}
                  barCategoryGap="20%"
                  barSize={20}
                  margin={{ left: -30 }}
                >
                  <CartesianGrid strokeDasharray="3 3" stroke="#F3F4F6" />
                  <XAxis
                    dataKey="date"
                    axisLine={false}
                    tickLine={false}
                    className="text-gray-600 text-sm font-medium"
                  />
                  <YAxis
                    axisLine={false}
                    tickLine={false}
                    domain={[
                      0,
                      (dataMax: number) => Math.ceil((dataMax + 5) / 10) * 10,
                    ]}
                    className="text-gray-600 text-sm font-semibold"
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Bar
                    dataKey="studyTime"
                    fill="#3B82F6"
                    radius={[4, 4, 0, 0]}
                  />
                  <Line
                    type="linear"
                    dataKey="targetTime"
                    stroke="#EAAA08"
                    strokeWidth={3}
                    strokeDasharray="5 5"
                    dot={false}
                    activeDot={false}
                  />
                </ComposedChart>
              </ResponsiveContainer>
            </div>
          </div>
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-8">
            <div className="flex items-center gap-2">
              <div className="w-6 h-1.5 bg-primary rounded-[4px] flex-shrink-0"></div>
              <span className="text-sm text-gray-600 font-medium">
                Số phút học trong ngày
              </span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-6 h-1.5 bg-warning rounded-[4px] flex-shrink-0"></div>
              <span className="text-sm text-gray-600 font-medium">
                Thời lượng mục tiêu
              </span>
            </div>
          </div>
        </>
      ) : (
        <div className="flex flex-col items-center gap-3 px-4">
          <img
            src="/illustrations/not-study-yet.png"
            className="w-full max-w-[300px] h-auto"
            alt="Not study yet illustration"
          />
          <div className="font-medium text-sm sm:text-base py-[11px] text-center max-w-md">
            Bạn chưa học ngày nào. Hệ thống sẽ ghi nhận khi bạn bắt đầu học!
          </div>
        </div>
      )}
    </div>
  );
};

export default PlanTimeChart;
