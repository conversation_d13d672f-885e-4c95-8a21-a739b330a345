import { defaultImage } from '@/helpers/image';
import SVG from 'react-inlinesvg';
import clsx from 'clsx';
import { IExamBookProductItem } from '@/types/book/index';
import { getDownloadUrlBookCover } from '@/utils/image';

const BookCardExchange = ({
  book,
  showChange,
  onSelect,
  isSelected,
  isDisabled,
}: {
  book: IExamBookProductItem;
  showChange: boolean;
  onSelect?: () => void;
  isSelected?: boolean;
  isDisabled?: boolean;
}) => {
  const handleCardClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    return;
  };

  const handleButtonClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (onSelect && !isDisabled) {
      onSelect();
    }
  };

  return (
    <div
      className={clsx(
        'relative w-full max-w-[255px] h-auto min-h-[252px] flex flex-col border-1px border-border rounded-lg group',
        'shadow-sm transition-transform duration-300 hover:shadow-[0_10px_40px_-4px_rgba(0,0,0,0.16)]',
        isSelected ? '' : 'border-gray-200 cursor-pointer',
        isDisabled ? '' : 'cursor-default',
      )}
      onClick={handleCardClick}
    >
      <div className="overflow-hidden rounded-t-lg">
        <img
          loading="lazy"
          src={getDownloadUrlBookCover(book.bookId)}
          alt={book.name}
          height={145}
          className="object-cover h-[145px] max-h-[145px] w-full transition-transform duration-300 group-hover:scale-[1.1]"
          onError={(e) => defaultImage(e, '/images/default-book-cover.jpg')}
        />
      </div>

      <div className="flex-1 flex flex-col gap-3 p-4">
        <div className="text-base text-title font-semibold leading-[1.3] line-clamp-1">
          {book.studyProgrammeName || book.studyProgramBook.name}
        </div>
        {book.description ? (
          <div className="text-sm font-medium text-gray-600 line-clamp-2 min-h-[42px]">
            {book.studyProgramBook?.subtitle}
          </div>
        ) : (
          <div className="text-sm font-medium text-gray-600 line-clamp-2 min-h-[42px]"></div>
        )}
        {isDisabled && (
          <div>
            <SVG src="/svg/frame-activated.svg"></SVG>
          </div>
        )}
        {showChange && !isDisabled && (
          <button
            className="w-full text-white text-[14px] leading-[21px] font-bold bg-[#4F83FC] rounded-lg py-2 hover:opacity-90 transition-colors break-words cursor-pointer"
            onClick={handleButtonClick}
          >
            Chọn
          </button>
        )}
      </div>
    </div>
  );
};

export default BookCardExchange;
