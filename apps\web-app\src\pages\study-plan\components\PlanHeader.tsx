import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@workspace/ui/components/popover';
import { Button } from '@workspace/ui/components/button';
import { LuPencilLine } from 'react-icons/lu';
import { MoreVertical, Trash2 } from 'lucide-react';
import { useState } from 'react';
import SVG from 'react-inlinesvg';

type Props = {
  setOpenUpsertModal: React.Dispatch<React.SetStateAction<boolean>>;
  setOpenStopModal: React.Dispatch<React.SetStateAction<boolean>>;
  setOpenDeleteModal: React.Dispatch<React.SetStateAction<boolean>>;
};

const PlanHeader = ({
  setOpenUpsertModal,
  setOpenStopModal,
  setOpenDeleteModal,
}: Props) => {
  const [openPopover, setOpenPopover] = useState(false);
  const handleClickEdit = () => {
    setOpenPopover(false);
    setOpenUpsertModal(true);
  };

  const handleClickStop = () => {
    setOpenPopover(false);
    setOpenStopModal(true);
  };

  const handleClickDelete = () => {
    setOpenPopover(false);
    setOpenDeleteModal(true);
  };

  return (
    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 sm:gap-4 w-full">
      <span className="font-semibold text-xl sm:text-2xl text-title py-2 break-words">
        Theo dõi mục tiêu hàng ngày
      </span>
      <Popover open={openPopover} onOpenChange={setOpenPopover}>
        <PopoverTrigger asChild>
          <Button
            variant="outline-light"
            size="sm"
            className="w-8 h-8 border-gray-400"
          >
            <MoreVertical className="text-gray-700 w-5 h-5" />
          </Button>
        </PopoverTrigger>
        <PopoverContent
          align="end"
          side="bottom"
          className="flex flex-col py-2 px-0 w-fit"
        >
          <div
            className="flex items-center gap-2 px-3 py-2 cursor-pointer hover:bg-gray-100 text-body"
            onClick={handleClickEdit}
          >
            <LuPencilLine className="w-5 h-5" />
            <span className="font-medium text-base">Chỉnh sửa mục tiêu</span>
          </div>
          <div
            className="flex items-center gap-2 px-3 py-2 cursor-pointer hover:bg-gray-100 text-body"
            onClick={handleClickStop}
          >
            <Trash2 className="w-5 h-5" />
            <span className="font-medium text-base">Kết thúc mục tiêu</span>
          </div>
          <div
            className="flex items-center gap-2 px-3 py-2 cursor-pointer hover:bg-gray-100 text-body"
            onClick={() => {}}
          >
            <SVG src="/svg/study-plan/rotate-clock.svg" className="w-5 h-5" />
            <span className="font-medium text-base">
              Xem mục tiêu đã kết thúc
            </span>
          </div>
          <div
            className="flex items-center gap-2 px-3 py-2 cursor-pointer hover:bg-gray-100 text-body"
            onClick={handleClickDelete}
          >
            <Trash2 className="w-5 h-5" />
            <span className="font-medium text-base">Xoá</span>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default PlanHeader;
